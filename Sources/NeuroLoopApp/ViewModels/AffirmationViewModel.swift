import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI

@MainActor
public class AffirmationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published private(set) var affirmations: [any NeuroLoopInterfaces.AffirmationProtocol] = []
    @Published private(set) var currentAffirmation: (any NeuroLoopInterfaces.AffirmationProtocol)?
    @Published private(set) var isLoading = false
    @Published var error: Error?
    @Published var isAutoRepeatEnabled = false
    @Published private(set) var statistics: AffirmationStatistics?
    @Published private(set) var streakStatistics: StreakStatistics?
    @Published public var mostRecentAffirmation: (any NeuroLoopInterfaces.AffirmationProtocol)?
    @Published public var mostRecentAffirmationTimestamp: Date?
    @Published private(set) var recentAffirmations: [any NeuroLoopInterfaces.AffirmationProtocol] =
        []
    @Published private(set) var recentActivities: [Activity] = []

    // MARK: - Private Properties

    let affirmationService: AffirmationServiceProtocol
    let repetitionService: RepetitionServiceProtocol
    let streakService: StreakServiceProtocol
    let audioRecordingService: AudioRecordingServiceProtocol
    private let hapticManager: NeuroLoopTypes.HapticGenerating
    private let themeManager: NeuroLoopInterfaces.ThemeManaging
    private var cancellables = Set<AnyCancellable>()
    private let mostRecentAffirmationKey = "mostRecentAffirmationID"
    private let mostRecentAffirmationTimestampKey = "mostRecentAffirmationTimestamp"
    private let maxRecentActivities = 5

    // MARK: - Initialization

    @MainActor
    public init(
        affirmationService: AffirmationServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        streakService: StreakServiceProtocol,
        audioRecordingService: AudioRecordingServiceProtocol,
        hapticManager: NeuroLoopTypes.HapticGenerating,
        themeManager: NeuroLoopInterfaces.ThemeManaging
    ) {
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        self.streakService = streakService
        self.audioRecordingService = audioRecordingService
        self.hapticManager = hapticManager
        self.themeManager = themeManager
        print(
            "[DEBUG] AffirmationViewModel init: affirmations initialized with count = \(affirmations.count)"
        )
        Task {
            await loadAffirmations()
            await loadStatistics()
        }
    }

    // MARK: - Public Methods

    /// Load all affirmations from the repository
    public func loadAffirmations() async {
        isLoading = true
        error = nil
        do {
            let fetchedAffirmations = try await affirmationService.fetchAffirmations()
            print("[DEBUG] loadAffirmations: fetched count = \(fetchedAffirmations.count)")
            affirmations = fetchedAffirmations
            currentAffirmation = try await affirmationService.fetchCurrentAffirmation()
            // Update recent affirmations (last 5 non-current affirmations)
            recentAffirmations = Array(
                affirmations
                    .filter { $0.id != currentAffirmation?.id }
                    .sorted { $0.createdAt > $1.createdAt }
                    .prefix(5))
            await loadMostRecentAffirmation()
        } catch {
            self.error = error
        }
        print("[DEBUG] loadAffirmations: affirmations now has count = \(affirmations.count)")
        isLoading = false
    }

    /// Load statistics
    public func loadStatistics() async {
        do {
            statistics = try await affirmationService.getStatistics()
            streakStatistics = try await streakService.getStreakStatistics()
        } catch {
            self.error = error
        }
    }

    /// Create a new affirmation
    public func createAffirmation(
        text: String, category: AffirmationCategory, recordingURL: URL? = nil
    ) async {
        isLoading = true
        error = nil
        var finalRecordingURL: URL? = recordingURL
        if let tempURL = recordingURL {
            do {
                let permanentURL = try moveAudioToPermanentLocation(tempURL)
                finalRecordingURL = permanentURL
            } catch {
                self.error = error
                isLoading = false
                return
            }
        }
        do {
            let createdAffirmation = try await affirmationService.createAffirmation(
                text: text,
                category: category,
                recordingURL: finalRecordingURL
            )
            await loadAffirmations()
            if currentAffirmation == nil {
                currentAffirmation = createdAffirmation
                try await startCycle(for: createdAffirmation)
            }
            updateActivitiesForNewAffirmation(createdAffirmation)
            await hapticManager.playSuccess()
            await loadStatistics()
        } catch {
            if let url = finalRecordingURL {
                try? FileManager.default.removeItem(at: url)
            }
            self.error = error
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// Move audio file to a permanent location associated with the affirmation
    private func moveAudioToPermanentLocation(_ tempURL: URL) throws -> URL {
        let fileManager = FileManager.default
        let directory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let permanentURL = directory.appendingPathComponent(UUID().uuidString + ".m4a")
        if fileManager.fileExists(atPath: permanentURL.path) {
            try fileManager.removeItem(at: permanentURL)
        }
        try fileManager.moveItem(at: tempURL, to: permanentURL)
        return permanentURL
    }

    /// Delete an affirmation
    public func deleteAffirmation(_ affirmation: any AffirmationProtocol) async {
        isLoading = true
        error = nil
        do {
            try await affirmationService.deleteAffirmation(id: affirmation.id)
            await loadAffirmations()
            await loadStatistics()
            await hapticManager.playSuccess()
        } catch {
            self.error = error
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// Toggle favorite status for an affirmation
    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async {
        let affirmationNL = affirmation as! any NeuroLoopInterfaces.AffirmationProtocol
        do {
            let updatedAffirmation = try await affirmationService.toggleFavorite(affirmationNL)
            if let index = affirmations.firstIndex(where: { $0.id == affirmationNL.id }) {
                affirmations[index] = updatedAffirmation
            }
            if currentAffirmation?.id == affirmationNL.id {
                currentAffirmation = updatedAffirmation
            }
            await hapticManager.playSelection()
            await loadStatistics()
        } catch {
            self.error = error
            await hapticManager.playError()
        }
    }

    /// Record a repetition for the current affirmation
    public func recordRepetition() async {
        guard let affirmation = currentAffirmation else { return }
        do {
            // Store the current repetition count for verification
            let beforeRepetitionCount = affirmation.currentRepetitions
            print("AffirmationViewModel: Before recordRepetition, affirmation.currentRepetitions: \(beforeRepetitionCount)")

            let result = try await repetitionService.recordRepetition(for: affirmation)

            if result.success {
                // Verify the updated affirmation has exactly one more repetition
                let afterRepetitionCount = result.updatedAffirmation.currentRepetitions
                print("AffirmationViewModel: After recordRepetition, updatedAffirmation.currentRepetitions: \(afterRepetitionCount)")
                print("AffirmationViewModel: Difference: \(afterRepetitionCount - beforeRepetitionCount)")

                // Update the current affirmation with the updated one
                currentAffirmation = result.updatedAffirmation

                // Update the affirmation in the list
                if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                    affirmations[index] = result.updatedAffirmation
                }

                // Update tracking and activities
                updateMostRecentAffirmation(result.updatedAffirmation)
                updateActivitiesForRepetition(result.updatedAffirmation)

                // NOTE: Notification is now posted by the underlying service to prevent duplicates
                // The MemoryStorageService or DebugRepetitionCounter will handle the notification
                print("AffirmationViewModel: Skipping notification post to prevent duplicates - handled by service layer")

                // Provide haptic feedback based on the result
                if result.isCycleComplete {
                    await hapticManager.playCelebrationPattern()
                } else if result.isQuotaMet {
                    await hapticManager.playAffirmationCompletionPattern()
                } else {
                    await hapticManager.mediumImpact()
                }

                // Update statistics
                await loadStatistics()
            } else if let error = result.error {
                self.error = error
                await hapticManager.playError()
            }
        } catch {
            self.error = error
            await hapticManager.playError()
        }
    }

    /// Start a new affirmation session
    public func startSession() async {
        isLoading = true
        error = nil
        do {
            // Load the most recent affirmation if none is current
            if currentAffirmation == nil {
                if let recent = mostRecentAffirmation {
                    currentAffirmation = recent
                } else {
                    // If no recent affirmation, fetch the first available one
                    let fetchedAffirmations = try await affirmationService.fetchAffirmations()
                    if let firstAffirmation = fetchedAffirmations.first {
                        currentAffirmation = firstAffirmation
                    }
                }
            }

            // Start a cycle for the current affirmation if needed
            if let affirmation = currentAffirmation {
                try await startCycle(for: affirmation)
            }

            // Load affirmations and statistics
            await loadAffirmations()
            await loadStatistics()

            await hapticManager.playSuccess()
        } catch {
            self.error = error
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// View progress statistics
    public func viewProgress() async {
        await loadStatistics()
    }

    /// Start a new cycle for an affirmation
    public func startCycle(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
        async throws
    {
        let result = try await repetitionService.startCycle(for: affirmation)
        if result.success {
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = result.updatedAffirmation
            }
            currentAffirmation = result.updatedAffirmation
            updateActivitiesForCycleStart(result.updatedAffirmation)
            await hapticManager.playSuccess()
            await loadStatistics()
        } else if let error = result.error {
            throw error
        }
    }

    /// Set the current affirmation
    public func setCurrentAffirmation(_ affirmation: any AffirmationProtocol) async {
        // If affirmation is already any NeuroLoopInterfaces.AffirmationProtocol, use it directly; otherwise, set currentAffirmation to nil (or handle as needed)
        if let casted = affirmation as? any NeuroLoopInterfaces.AffirmationProtocol {
            // Check if we're actually changing the affirmation
            let isChangingAffirmation = currentAffirmation?.id != casted.id

            if isChangingAffirmation {
                print("AffirmationViewModel: Changing current affirmation from \(currentAffirmation?.id.uuidString ?? "nil") to \(casted.id.uuidString)")

                // Update the current affirmation
                currentAffirmation = casted

                // Update tracking and activities
                updateMostRecentAffirmation(casted)
                updateActivitiesForCurrentAffirmation(casted)

                // Notify observers that the current affirmation has changed
                NotificationCenter.default.post(
                    name: Notification.Name("CurrentAffirmationChanged"),
                    object: nil,
                    userInfo: ["affirmationId": casted.id]
                )

                // Start a cycle if needed
                if let hasActiveCycle = (casted as? (any NeuroLoopInterfaces.AffirmationProtocol & AnyObject))?.hasActiveCycle,
                   !hasActiveCycle {
                    do {
                        try await startCycle(for: casted)
                    } catch {
                        self.error = error
                        await hapticManager.playError()
                    }
                }
            } else {
                print("AffirmationViewModel: Current affirmation unchanged: \(casted.id.uuidString)")
            }
        } else {
            currentAffirmation = nil

            // Notify observers that the current affirmation has been cleared
            NotificationCenter.default.post(
                name: Notification.Name("CurrentAffirmationChanged"),
                object: nil,
                userInfo: ["affirmationId": UUID()]
            )
        }
    }

    /// Start audio recording
    public func startRecording() async {
        do {
            try await audioRecordingService.startRecording()
        } catch {
            self.error = error
            await hapticManager.playError()
        }
    }

    /// Stop recording audio and return the file URL
    public func stopRecording() async -> URL? {
        do {
            return try await audioRecordingService.stopRecording()
        } catch {
            self.error = error
            await hapticManager.playError()
            return nil
        }
    }

    /// Start audio playback
    public func startPlayback() async {
        do {
            try await audioRecordingService.startPlayback()
        } catch {
            self.error = error
            await hapticManager.playError()
        }
    }

    /// Validate streaks
    public func validateStreaks() async {
        do {
            let result = try await streakService.validateStreaks()
            if result.success {
                await loadAffirmations()
                await loadStatistics()
                if result.brokenStreaks > 0 {
                    await hapticManager.playError()
                }
            } else if let error = result.error {
                self.error = error
                await hapticManager.playError()
            }
        } catch {
            self.error = error
            await hapticManager.playError()
        }
    }

    /// Get streak calendar data for the current affirmation
    public func getStreakCalendarData(numberOfDays: Int = 30) async -> [NeuroLoopInterfaces.StreakCalendarDay] {
        guard let affirmation = currentAffirmation else { return [] }
        return await streakService.getStreakCalendarData(
            for: affirmation, numberOfDays: numberOfDays)
    }

    /// Check if the current streak is at risk
    public func isCurrentStreakAtRisk() async -> Bool {
        guard let affirmation = currentAffirmation else { return false }
        return await streakService.isStreakAtRisk(for: affirmation)
    }

    // MARK: - Most Recent Affirmation Tracking
    private func updateMostRecentAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        mostRecentAffirmation = affirmation
        mostRecentAffirmationTimestamp = Date()
        let defaults = UserDefaults.standard
        defaults.set(affirmation.id.uuidString, forKey: mostRecentAffirmationKey)
        defaults.set(Date(), forKey: mostRecentAffirmationTimestampKey)
    }

    private func loadMostRecentAffirmation() async {
        let defaults = UserDefaults.standard
        guard let idString = defaults.string(forKey: mostRecentAffirmationKey),
            let id = UUID(uuidString: idString)
        else {
            mostRecentAffirmation = nil
            mostRecentAffirmationTimestamp = nil
            return
        }
        mostRecentAffirmationTimestamp =
            defaults.object(forKey: mostRecentAffirmationTimestampKey) as? Date
        if let affirmation = affirmations.first(where: { $0.id == id }) {
            mostRecentAffirmation = affirmation
        } else {
            // Try to fetch from service if not in memory
            if let fetched = try? await affirmationService.fetchAffirmation(id: id) {
                mostRecentAffirmation = fetched
            } else {
                // Affirmation was deleted
                mostRecentAffirmation = nil
                defaults.removeObject(forKey: mostRecentAffirmationKey)
                defaults.removeObject(forKey: mostRecentAffirmationTimestampKey)
            }
        }
    }

    // MARK: - Activity Management

    private func addActivity(title: String, subtitle: String, icon: String) {
        let activity = Activity(
            title: title,
            subtitle: subtitle,
            icon: icon,
            time: formatTimeAgo(Date())
        )
        recentActivities.insert(activity, at: 0)
        if recentActivities.count > maxRecentActivities {
            recentActivities.removeLast()
        }
    }

    private func formatTimeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    // MARK: - Activity Updates

    private func updateActivitiesForRepetition(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "Repetition Recorded"
        let subtitle = affirmation.text
        let icon = "checkmark.circle.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForNewAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "New Affirmation Created"
        let subtitle = affirmation.text
        let icon = "plus.circle.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForCycleStart(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "New Cycle Started"
        let subtitle = affirmation.text
        let icon = "arrow.triangle.2.circlepath"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForCurrentAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "Current Affirmation Changed"
        let subtitle = affirmation.text
        let icon = "star.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    // MARK: - Computed Properties

    public var theme: Theme {
        themeManager.currentTheme
    }

    public var hasAffirmations: Bool {
        let result = !affirmations.isEmpty
        print(
            "[DEBUG] hasAffirmations accessed: \(result), affirmations count = \(affirmations.count)"
        )
        return result
    }

    public var canRecordRepetition: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.canPerformRepetition(for: affirmation)
    }

    public var currentProgress: Double {
        guard let affirmation = currentAffirmation else { return 0.0 }
        return repetitionService.getProgress(for: affirmation).todayProgress
    }

    public var currentCycleProgress: Double {
        guard let affirmation = currentAffirmation else { return 0.0 }
        return repetitionService.getProgress(for: affirmation).cycleProgress
    }

    public var currentRepetitions: Int {
        guard let affirmation = currentAffirmation else { return 0 }
        return repetitionService.getProgress(for: affirmation).currentRepetitions
    }

    public var totalRepetitions: Int {
        AffirmationConstants.DAILY_REPETITIONS
    }

    public var currentCycleDay: Int {
        guard let affirmation = currentAffirmation else { return 1 }
        return repetitionService.getProgress(for: affirmation).currentDay
    }

    public var totalCycleDays: Int {
        AffirmationConstants.CYCLE_DAYS
    }

    public var isCurrentCycleComplete: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.getProgress(for: affirmation).isCycleComplete
    }

    public var hasTodayQuotaMet: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.getProgress(for: affirmation).hasTodayQuotaMet
    }

    public var timeUntilNextRepetition: TimeInterval? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.timeUntilNextRepetition(for: affirmation)
    }

    public var streakInfo: StreakInfo? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.getStreakInfo(for: affirmation)
    }

    public var progressInfo: ProgressInfo? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.getProgress(for: affirmation)
    }
}
