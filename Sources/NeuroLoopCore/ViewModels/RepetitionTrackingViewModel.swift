import Combine
import Foundation
import NeuroLoopInterfaces
import OSLog
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class RepetitionTrackingViewModel: ObservableObject {
    // MARK: - Types

    public struct RepetitionHistoryEntry: Identifiable, Sendable {
        public let id: UUID
        public let date: Date
        public let count: Int

        public init(id: UUID = UUID(), date: Date, count: Int) {
            self.id = id
            self.date = date
            self.count = count
        }
    }

    // MARK: - Properties

    private let logger = Logger(
        subsystem: "com.neuroloop.NeuroLoop100", category: "RepetitionTracking")

    private let affirmationService: AffirmationServiceProtocol
    @Published public var repetitionService: RepetitionServiceProtocol
    private let streakService: StreakServiceProtocol
    private let audioService: AudioRecordingServiceProtocol

    @Published public private(set) var affirmation: (any AffirmationProtocol & Sendable)?
    @Published public private(set) var todayProgress: Double = 0
    @Published public private(set) var cycleProgress: Double = 0
    @Published public private(set) var todayRepetitions: Int = 0
    @Published public private(set) var dailyGoal: Int = 15
    @Published public private(set) var currentDay: Int = 1
    @Published public private(set) var streakDays: [StreakCalendarDay] = []
    @Published public private(set) var isLoading: Bool = false
    @Published public private(set) var error: Error? = nil
    @Published public private(set) var canPerformRepetition: Bool = true
    @Published public private(set) var isMilestoneReached: Bool = false
    @Published public private(set) var hasRecording: Bool = false
    @Published public private(set) var isPlaying: Bool = false
    @Published public private(set) var playbackProgress: Double = 0
    @Published public var showRestorationFlow: Bool = false
    @Published public private(set) var recentHistory: [RepetitionHistoryEntry] = []

    private var cancellables: Set<AnyCancellable> = []

    public init(
        affirmationService: AffirmationServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        streakService: StreakServiceProtocol,
        audioService: AudioRecordingServiceProtocol
    ) {
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        self.streakService = streakService
        self.audioService = audioService

        setupAudioBindings()
    }

    private func setupAudioBindings() {
        audioService.isPlayingPublisher
            .receive(on: DispatchQueue.main)
            .assign(to: &$isPlaying)

        audioService.playbackProgressPublisher
            .receive(on: DispatchQueue.main)
            .assign(to: &$playbackProgress)
    }

    public func loadData(for affirmationId: UUID) async {
        logger.debug("Loading data for affirmation: \(affirmationId)")
        isLoading = true
        error = nil

        do {
            if let fetchedAffirmation = try await affirmationService.fetchAffirmation(
                id: affirmationId)
            {
                affirmation = fetchedAffirmation
                logger.debug("Successfully loaded affirmation: \(fetchedAffirmation.id)")
                updateProgress(for: fetchedAffirmation)
                hasRecording = fetchedAffirmation.recordingURL != nil
            }
        } catch {
            logger.error("Failed to load affirmation: \(error.localizedDescription)")
            self.error = error
        }

        isLoading = false
    }

    /// Convenience method to reload data for the current affirmation
    public func loadData() async {
        guard let affirmation = self.affirmation else {
            logger.debug("No affirmation available to load data for")
            return
        }
        await loadData(for: affirmation.id)
    }

    private func updateProgress(for affirmation: any AffirmationProtocol & Sendable) {
        let progress = repetitionService.getProgress(for: affirmation)
        todayProgress = progress.todayProgress
        cycleProgress = progress.cycleProgress
        currentDay = progress.currentDay
        dailyGoal = progress.totalRepetitions
        todayRepetitions = progress.currentRepetitions
        canPerformRepetition = repetitionService.canPerformRepetition(for: affirmation)
    }

    /// Performs a repetition for the current affirmation
    /// This is the SINGLE SOURCE OF TRUTH for recording repetitions
    public func performRepetition() async {
        guard let affirmation = self.affirmation else {
            logger.error("Attempted to perform repetition without loaded affirmation")
            return
        }

        logger.debug("🎯 CORE: performRepetition called for affirmation: \(affirmation.id)")
        logger.debug("🎯 CORE: Current count before: \(self.todayRepetitions)")

        do {
            let result = try await repetitionService.recordRepetition(for: affirmation)
            logger.debug("🎯 CORE: Successfully recorded repetition")

            // Update the affirmation with the new data
            self.affirmation = result.updatedAffirmation
            logger.debug("🎯 CORE: Updated affirmation object with new repetition count: \(result.updatedAffirmation.currentRepetitions)")

            // Update progress based on the updated affirmation
            updateProgress(for: result.updatedAffirmation)

            // Milestone logic: quota met
            if result.isQuotaMet {
                logger.info("🎯 CORE: Milestone reached for affirmation: \(affirmation.id)")
                isMilestoneReached = true
                // Reset after a delay
                Task { @MainActor in
                    try? await Task.sleep(for: .seconds(3))
                    self.isMilestoneReached = false
                }
            }

            // Update streak data
            try await streakService.updateStreak(for: result.updatedAffirmation)
            logger.debug("🎯 CORE: Updated streak data")

            // Force UI update on main thread
            await MainActor.run {
                self.objectWillChange.send()
                logger.debug("🎯 CORE: Sent objectWillChange signal, new count: \(self.todayRepetitions)")
            }

            // Post notification for any other observers
            await MainActor.run {
                NotificationCenter.default.post(
                    name: Notification.Name("RepetitionCountChanged"),
                    object: nil,
                    userInfo: [
                        "affirmationId": result.updatedAffirmation.id,
                        "count": result.updatedAffirmation.currentRepetitions,
                        "source": "RepetitionTrackingViewModel"
                    ]
                )
                logger.debug("🎯 CORE: Posted RepetitionCountChanged notification")
            }

        } catch {
            logger.error("🎯 CORE: Failed to perform repetition: \(error.localizedDescription)")
            self.error = error
        }
    }

    public func playRecording() async {
        guard let affirmation = self.affirmation,
            affirmation.recordingURL != nil
        else {
            logger.error("Attempted to play recording without valid URL")
            return
        }

        logger.debug("Starting playback for affirmation: \(affirmation.id)")

        do {
            try await audioService.startPlayback()
            logger.debug("Successfully started playback")
        } catch {
            logger.error("Failed to start playback: \(error.localizedDescription)")
            self.error = error
        }
    }

    public func pausePlayback() {
        logger.debug("Pausing playback")
        audioService.pausePlayback()
    }

    func retry() {
        logger.debug("Retrying operation")
        if let affirmation = self.affirmation {
            Task { @MainActor in
                await loadData(for: affirmation.id)
            }
        }
    }

    func dismissError() {
        logger.debug("Dismissing error")
        error = nil
    }

    /// Updates the repetition count directly - used for syncing with other view models
    /// Simplified version that focuses on reliable UI updates
    public func updateRepetitionCount(_ count: Int) {
        logger.debug("updateRepetitionCount called with \(count), current: \(self.todayRepetitions)")

        // Store the old value for comparison
        let oldCount = self.todayRepetitions

        // Update the count immediately
        self.todayRepetitions = count

        // Update related progress values if we have an affirmation
        if let affirmation = self.affirmation {
            let progress = self.repetitionService.getProgress(for: affirmation)
            self.todayProgress = progress.todayProgress
            self.cycleProgress = progress.cycleProgress
        }

        logger.debug("Updated count from \(oldCount) to \(count)")
        logger.debug("Progress - today: \(self.todayProgress), cycle: \(self.cycleProgress)")

        // Force immediate UI update
        self.objectWillChange.send()

        logger.debug("Sent objectWillChange signal")
    }

    /// Updates the repetition service - used when switching between debug and normal modes
    public func updateRepetitionService(_ newService: RepetitionServiceProtocol) {
        logger.debug("Updating repetition service")
        repetitionService = newService

        // Force UI update
        objectWillChange.send()

        // Reload data to ensure we have the latest state
        if let affirmation = self.affirmation {
            Task {
                await loadData(for: affirmation.id)
            }
        }
    }

    /// Updates the affirmation object - used when the affirmation is updated elsewhere
    public func updateAffirmation(_ updatedAffirmation: any AffirmationProtocol & Sendable) {
        logger.debug("Updating affirmation: \(updatedAffirmation.id)")
        affirmation = updatedAffirmation

        // Update progress data based on the new affirmation
        updateProgress(for: updatedAffirmation)

        // Force UI update
        objectWillChange.send()

        logger.debug("Updated affirmation and progress data")
        logger.debug("New repetition count: \(self.todayRepetitions)")
    }

    /// Resets today's repetition count to zero - used for debug purposes
    public func resetTodayRepetitions() async {
        logger.debug("Resetting today's repetitions to zero")

        // If we're using a DebugRepetitionService, reset its counter too
        if let debugService = repetitionService as? DebugRepetitionService,
           let affirmation = self.affirmation {
            await MainActor.run {
                debugService.resetCount(for: affirmation.id)
                logger.debug("Reset debug repetition counter for affirmation \(affirmation.id)")
            }
        }

        // Update the count to zero
        await MainActor.run {
            self.todayRepetitions = 0
            self.todayProgress = 0.0

            // Force UI update
            self.objectWillChange.send()
        }

        logger.debug("Reset today's repetitions to zero")
    }
}
