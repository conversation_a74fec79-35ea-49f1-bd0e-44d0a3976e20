import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

import SwiftUI

#if os(iOS) || os(tvOS)
    import UIKit
#elseif os(macOS)
    import AppKit
#endif

/// A temporary version of SpeakAffirmationView that doesn't use the new components
/// This is a fallback until we resolve the import issues
@available(iOS 17.0, macOS 14.0, *)
public struct SpeakAffirmationViewTemp: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var viewModel: SpeakAffirmationViewModel
    @State private var showingConfirmation = false
    @State private var forceRefreshID = UUID()  // Used to force view refresh

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        affirmationService: AffirmationServiceProtocol,
        streakService: StreakServiceProtocol
    ) {
        _viewModel = StateObject(
            wrappedValue: SpeakAffirmationViewModel(
                affirmation: affirmation,
                repetitionService: repetitionService,
                audioService: Self.createAudioService(),
                affirmationService: affirmationService,
                streakService: streakService
            ))
    }

    private static func createAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            return AudioRecordingService()
        #endif
    }

    // MARK: - Helper Methods

    // Function to determine what message to display in the status box
    private func getStatusMessage() -> String {
        if viewModel.isRecording {
            if !viewModel.partialRecognitionText.isEmpty {
                return viewModel.partialRecognitionText
            } else {
                return "Listening..."
            }
        } else {
            return "Tap to start recording"
        }
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Use a custom blue gradient directly
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.1, green: 0.4, blue: 0.8),
                    Color(red: 0.0, green: 0.2, blue: 0.6),
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            // Content
            VStack(spacing: 24) {  // Increased overall spacing
                // Add more space at the top to avoid status bar and back button
                Spacer()
                    .frame(height: 160)  // Increased from 140 to 160 to move content down further

                // Repetition Progress Card
                VStack {
                    // Debug print to track repetition count (moved to onAppear)
                    let progressCardId = "progress-card-\(viewModel.todayRepetitions)-\(UUID())"

                    RepetitionProgressCardView(
                        currentCount: viewModel.todayRepetitions,
                        totalCount: viewModel.totalRepetitions,
                        currentDay: viewModel.currentDay,
                        totalDays: 7,
                        affirmationId: viewModel.affirmation.id,  // Pass the affirmation ID
                        onTap: {
                            // Optionally, you can trigger a haptic or other action here
                            print("SpeakAffirmationView: RepetitionProgressCardView tapped")
                            print(
                                "SpeakAffirmationView: Current count: \(viewModel.todayRepetitions)"
                            )
                            print(
                                "SpeakAffirmationView: Affirmation ID: \(viewModel.affirmation.id)")

                            // If in debug mode, force increment the repetition count
                            if viewModel.debugBypassSpeechRecognition {
                                print(
                                    "SpeakAffirmationView: Debug mode enabled, calling forceIncrementRepetition"
                                )
                                Task {
                                    await viewModel.forceIncrementRepetition()
                                }
                            } else {
                                print(
                                    "SpeakAffirmationView: Debug mode disabled, tap has no effect")
                            }
                        }
                    )
                    .id(progressCardId)  // Force view to recreate when count changes
                }
                .padding([.top, .bottom], 10)  // Slightly increased vertical padding

                // Affirmation Text - using AffirmationTextView instead of AffirmationCardContentView
                AffirmationTextView(
                    text: viewModel.affirmation.text,
                    showRepeatExactly: true,
                    onRepeatExactly: {
                        // Call the playRecording method
                        Task {
                            await viewModel.playRecording()
                        }
                    },
                    isRecording: viewModel.isRecording
                )
                .padding()
                .blueCard()
                .padding(.horizontal)

                // Recording Button
                VStack(spacing: 8) {  // Reduced spacing
                    ZStack {
                        // Main recording button
                        RecordingButton(
                            isRecording: viewModel.isRecording,
                            onTap: {
                                print(
                                    "Recording button tapped, current state: \(viewModel.isRecording ? "recording" : "not recording")"
                                )
                                Task {
                                    if viewModel.isRecording {
                                        print("Stopping recording...")
                                        await viewModel.stopRecording()
                                    } else {
                                        print("Starting recording...")
                                        do {
                                            try await viewModel.startRecording()
                                        } catch {
                                            print("Error starting recording: \(error)")
                                            viewModel.alertItem = AlertItem(
                                                title: "Recording Error",
                                                message: error.localizedDescription)
                                        }
                                    }
                                }
                            },
                            haptics: HapticManager.shared,
                            audioLevel: viewModel.currentAudioLevel
                        )
                        .scaleEffect(1.2)  // Make the button 20% larger
                        .accessibilityLabel(
                            viewModel.isRecording ? "Stop recording" : "Start recording"
                        )
                        .accessibilityHint(
                            "Double tap to \(viewModel.isRecording ? "stop" : "start") recording your affirmation"
                        )
                        .onAppear {
                            print("Recording button appeared, checking audio permissions...")
                            // Initialize the view model if needed
                            Task {
                                await viewModel.initialize()
                            }
                        }

                        // No overlay needed - removed
                    }

                    // Simplified message text without background box
                    Text(getStatusMessage())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(width: 200, height: 30)
                }
                .padding(.bottom, 30)
            }
            .padding()

            // Alert for errors or success messages
            .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
                return Alert(
                    title: Text(alertItem.title),
                    message: Text(alertItem.message),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
        .onAppear {
            // Debug prints for repetition progress tracking
            print("SpeakAffirmationView: View appeared")
            print(
                "SpeakAffirmationView: Rendering with todayRepetitions=\(viewModel.todayRepetitions)"
            )
        }
        .onDisappear {
            // Clean up resources when the view disappears
            print("SpeakAffirmationView: View is disappearing, cleaning up resources")
            Task {
                await viewModel.stopRecording()
            }
        }
        .id(forceRefreshID)  // Force the entire view to refresh when this changes
    }
}
