import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

#if os(iOS) || os(tvOS)
    import UIKit
#elseif os(macOS)
    import AppKit
#endif

/// A view for speaking and recording affirmations with a modern UI
@available(iOS 17.0, macOS 14.0, *)
public struct SpeakAffirmationView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var viewModel: FixedSpeakAffirmationViewModel
    @StateObject private var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel
    @State private var showingConfirmation = false
    @State private var forceRefreshID = UUID()  // Used to force view refresh
    @State private var progressCardId = UUID()  // Used to force progress card refresh

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        affirmationService: AffirmationServiceProtocol,
        streakService: StreakServiceProtocol
    ) {
        _viewModel = StateObject(
            wrappedValue: FixedSpeakAffirmationViewModel(
                affirmation: affirmation,
                repetitionService: repetitionService,
                audioService: Self.createAudioService(),
                affirmationService: affirmationService
            ))

        _trackingViewModel = StateObject(
            wrappedValue: NeuroLoopCore.RepetitionTrackingViewModel(
                affirmationService: affirmationService,
                repetitionService: repetitionService,
                streakService: streakService,
                audioService: Self.createAudioService()
            ))
    }

    private static func createAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            return AudioRecordingService()
        #endif
    }

    // MARK: - Helper Methods

    // Function to determine what message to display in the status box
    private func getStatusMessage() -> String {
        if viewModel.isRecording {
            if !viewModel.partialRecognitionText.isEmpty {
                return viewModel.partialRecognitionText
            } else {
                return "Listening..."
            }
        } else {
            return "Tap to start recording"
        }
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Use a custom blue gradient directly
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.1, green: 0.4, blue: 0.8),
                    Color(red: 0.0, green: 0.2, blue: 0.6),
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            // DEBUG CONTROLS - positioned in top right corner without affecting layout
            VStack {
                HStack {
                    Spacer()
                    VStack(spacing: 8) {
                        // Testing Mode Toggle
                        HStack {
                            Text("Testing Mode:")
                                .font(.caption)
                                .foregroundColor(.white)
                            Toggle("", isOn: $viewModel.debugBypassSpeechRecognition)
                                .scaleEffect(0.8)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.black.opacity(0.3))
                        .cornerRadius(8)

                        // Debug buttons - only show when testing mode is ON
                        if viewModel.debugBypassSpeechRecognition {
                            VStack(spacing: 4) {
                                Button("Shared Counter +1") {
                                    Task {
                                        await trackingViewModel.performRepetition()
                                        print("DEBUG: Manual repetition added via debug button")
                                    }
                                }
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green.opacity(0.8))
                                .foregroundColor(.white)
                                .cornerRadius(6)

                                Button("Reset Count") {
                                    Task {
                                        await trackingViewModel.resetTodayRepetitions()
                                        print("DEBUG: Reset repetition count via debug button")
                                    }
                                }
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.red.opacity(0.8))
                                .foregroundColor(.white)
                                .cornerRadius(6)
                            }
                        }
                    }
                    .padding(.trailing, 16)
                    .padding(.top, 60) // Position below status bar
                }
                Spacer()
            }

            // Content
            VStack(spacing: 24) {  // Increased overall spacing
                // Add more space at the top to avoid status bar and back button
                Spacer()
                    .frame(height: 160)  // Increased from 140 to 160 to move content down further

                // Repetition Progress Card (Reset button moved below recording button)
                VStack {
                    // Debug print to track repetition count (moved to onAppear)
                    let progressCardIdString =
                        "progress-card-\(viewModel.todayRepetitions)-\(progressCardId)"

                    RepetitionProgressCardView(
                        viewModel: trackingViewModel
                    )
                    .id(progressCardIdString)  // Force view to recreate when count changes
                }
                .padding([.top, .bottom], 10)  // Slightly increased vertical padding

                // Affirmation Text with word highlighting
                // Use AffirmationCardContentView as a fallback until we resolve the import issue
                AffirmationCardContentView(
                    text: viewModel.affirmation.text,
                    showRepeatExactly: true,
                    onRepeatExactly: {
                        // Call the playRecording method
                        Task {
                            await viewModel.playRecording()
                        }
                    },
                    isRecording: viewModel.isRecording
                )
                .padding(.top, 4)
                .padding(.bottom, 4)  // Reduced vertical padding

                // Recording Button
                VStack(spacing: 8) {  // Reduced spacing
                    ZStack {
                        // Main recording button
                        RecordingButton(
                            isRecording: viewModel.isRecording,
                            onTap: {
                                print(
                                    "Recording button tapped, current state: \(viewModel.isRecording ? "recording" : "not recording")"
                                )
                                Task {
                                    if viewModel.isRecording {
                                        print("Stopping recording...")
                                        await viewModel.stopRecording()

                                        // Add a delay after stopping recording before updating UI
                                        // This ensures the recording button has fully completed its state change
                                        try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

                                        // Force refresh the progress card
                                        DispatchQueue.main.async {
                                            progressCardId = UUID()
                                            forceRefreshID = UUID()
                                            print(
                                                "SpeakAffirmationView: Forced refresh after stopping recording"
                                            )
                                        }
                                    } else {
                                        print("Starting recording...")
                                        viewModel.startRecording()
                                    }
                                }
                            },
                            haptics: HapticManager.shared,
                            audioLevel: viewModel.currentAudioLevel
                        )
                        .scaleEffect(1.2)  // Make the button 20% larger
                        .accessibilityLabel(
                            viewModel.isRecording ? "Stop recording" : "Start recording"
                        )
                        .accessibilityHint(
                            "Double tap to \(viewModel.isRecording ? "stop" : "start") recording your affirmation"
                        )
                        .onAppear {
                            print("Recording button appeared, checking audio permissions...")
                        }
                    }

                    // Simplified message text without background box
                    Text(getStatusMessage())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        // Use individual modifiers instead of fixedSize
                        .lineLimit(nil)
                        .frame(minWidth: 200, minHeight: 36)
                        .padding(.top, 10)
                }
                .padding(.top, 12)  // Add more space between button and text
                .padding(.vertical, 10)  // Reduced vertical padding for the whole recording section

                // Add bottom safe area spacing to avoid tab bar overlap
                Spacer(minLength: 100)  // Reduced from 120 to 100 to balance the increased top spacing
            }
            .padding(.horizontal, 24)  // Further increased horizontal padding
            .padding(.bottom, 120)  // Further increased bottom padding to avoid tab bar
            .padding(.top, 0)  // Removed top padding since we added a larger spacer
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)  // Ensure content aligns from the top
        }
        .alert("Exit Session?", isPresented: $showingConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Exit", role: .destructive) {
                // Clean up resources before dismissing
                Task {
                    await viewModel.stopRecording()
                    dismiss()
                }
            }
        } message: {
            Text("Your progress for this session will not be saved.")
        }
        .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
            return Alert(
                title: Text(alertItem.title),
                message: Text(alertItem.message),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // Debug prints for repetition progress tracking
            print("SpeakAffirmationView: View appeared")
            print(
                "SpeakAffirmationView: Rendering with todayRepetitions=\(viewModel.todayRepetitions)"
            )
            print("SpeakAffirmationView: Affirmation ID: \(viewModel.affirmation.id)")
            print("SpeakAffirmationView: Current day: \(viewModel.currentDay)")
            print("SpeakAffirmationView: Total repetitions: \(viewModel.totalRepetitions)")

            // Connect the view models
            viewModel.trackingViewModel = trackingViewModel
            print("SpeakAffirmationView: Connected trackingViewModel to viewModel")

            // Load data in the tracking view model with the affirmation ID
            Task {
                await trackingViewModel.loadData(for: viewModel.affirmation.id)

                // CRITICAL: Set the current affirmation in the tracking view model
                await MainActor.run {
                    trackingViewModel.updateAffirmation(viewModel.affirmation)
                    print("SpeakAffirmationView: Set current affirmation in tracking view model: \(viewModel.affirmation.text)")
                }

                print("SpeakAffirmationView: Loaded data in trackingViewModel")
            }

            // Debug mode should be disabled by default for production builds
            viewModel.debugBypassSpeechRecognition = false
            print("SpeakAffirmationView: Debug mode disabled by default")

            // Set up notification observer for repetition count changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("RepetitionCountChanged"), object: nil, queue: .main
            ) { [self] notification in
                print("SpeakAffirmationView: Received RepetitionCountChanged notification")
                print(
                    "SpeakAffirmationView: Notification userInfo: \(notification.userInfo ?? [:])")

                // Only update if the notification is for this affirmation
                if let affirmationId = notification.userInfo?["affirmationId"] as? UUID,
                    affirmationId == viewModel.affirmation.id,
                    let count = notification.userInfo?["count"] as? Int
                {
                    print(
                        "SpeakAffirmationView: Received notification of count change to \(count) for affirmation \(affirmationId)"
                    )
                    print(
                        "SpeakAffirmationView: Current todayRepetitions: \(viewModel.todayRepetitions)"
                    )

                    // Update the tracking view model
                    trackingViewModel.updateRepetitionCount(count)

                    // Force refresh the view
                    forceRefreshID = UUID()
                    print("SpeakAffirmationView: Generated new forceRefreshID: \(forceRefreshID)")
                } else {
                    print(
                        "SpeakAffirmationView: Notification is for a different affirmation or missing count"
                    )
                }
            }
        }
        .onDisappear {
            // Clean up resources when the view disappears
            print("SpeakAffirmationView: View is disappearing, cleaning up resources")
            Task {
                await viewModel.stopRecording()
            }
        }
        .onChange(of: viewModel.debugBypassSpeechRecognition) { oldValue, newValue in
            print("SpeakAffirmationView: Testing mode changed to \(newValue)")

            // Update the tracking view model with the appropriate service
            Task {
                let factory = ServiceFactory.shared
                do {
                    // Get the appropriate repetition service based on the new debug mode
                    let service = try factory.getRepetitionServiceForMode(debugMode: newValue)
                    print("SpeakAffirmationView: Got \(newValue ? "debug" : "standard") repetition service: \(type(of: service))")

                    // Update the tracking view model
                    trackingViewModel.updateRepetitionService(service)
                    print("SpeakAffirmationView: Updated tracking view model with \(newValue ? "debug" : "standard") service")
                } catch {
                    print("SpeakAffirmationView: Error getting repetition service: \(error)")
                }
            }
        }
        .id(forceRefreshID)  // Force the entire view to refresh when this changes
    }
}

// MARK: - Affirmation Card Content View

@available(iOS 17.0, macOS 14.0, *)
private struct AffirmationCardContentView: View {
    let text: String
    var showRepeatExactly: Bool = false
    var onRepeatExactly: (() -> Void)? = nil
    var isRecording: Bool = false
    @State private var showingTips = false

    // Animation properties
    @State private var animationTrigger = false

    var body: some View {
        ZStack {
            // Use a custom blue card background directly
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.0, green: 0.5, blue: 1.0),
                            Color(red: 0.0, green: 0.3, blue: 0.8),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)

            VStack(spacing: 16) {
                HStack {
                    // Left quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.leading, -10)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)

                    if showRepeatExactly {
                        // Repeat Exactly button
                        Button(action: {
                            onRepeatExactly?()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.white)
                                    .font(.system(size: 12))
                                Text("Repeat Exactly")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.3))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                            // Add a subtle shadow for better visibility
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                        .padding(.leading, 8)
                    }

                    Spacer()

                    Button(action: {
                        // Show hints or tips when tapped
                        showingTips = true
                    }) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.title3)
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                            .padding(8)
                            .contentShape(Rectangle())
                    }
                    .accessibilityLabel("Affirmation Tips")
                    .accessibilityHint("Double tap to show tips for effective affirmations")
                }

                // Main affirmation text (simple, no highlighting)
                Text(text)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .fixedSize(horizontal: false, vertical: true)  // Allow text to expand vertically
                    .minimumScaleFactor(0.7)  // Scale down text if needed to fit
                    .lineSpacing(4)  // Add some space between lines
                    .padding(.horizontal)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                HStack {
                    Spacer()

                    // Right quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.trailing, -10)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)
                }
            }
            .padding()
        }
        .padding(.horizontal)
        .sheet(isPresented: $showingTips) {
            TipsView()
        }
    }
}

// MARK: - Tips View

@available(iOS 17.0, macOS 14.0, *)
private struct TipsView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Tips for Effective Affirmations")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.bottom, 10)

                    TipItem(
                        icon: "text.quote",
                        title: "Speak Clearly",
                        description: "Pronounce each word clearly and with conviction."
                    )

                    TipItem(
                        icon: "ear",
                        title: "Listen to Yourself",
                        description:
                            "Pay attention to how your voice sounds. Speak with confidence."
                    )

                    TipItem(
                        icon: "arrow.clockwise",
                        title: "Repeat Exactly",
                        description:
                            "Use the 'Repeat Exactly' button to hear the correct pronunciation."
                    )

                    TipItem(
                        icon: "chart.bar.fill",
                        title: "Track Progress",
                        description: "Complete your daily repetitions to build a streak."
                    )

                    TipItem(
                        icon: "heart.fill",
                        title: "Feel the Meaning",
                        description: "Connect emotionally with the words as you speak them."
                    )
                }
                .padding()
            }
            .navigationTitle("Affirmation Tips")
            .toolbar {
                #if os(iOS) || os(watchOS) || os(tvOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #else
                    ToolbarItem(placement: .automatic) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #endif
            }
        }
    }
}

private struct TipItem: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
}

// RepeatExactlyButton removed - now integrated into the affirmation card

// MARK: - Wave Shape

/// Shape that draws a sine wave with configurable parameters
struct WaveShape: Shape {
    var phase: Double
    var amplitude: Double
    var frequency: Double

    // Make the shape animatable
    var animatableData: Double {
        get { phase }
        set { phase = newValue }
    }

    func path(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let midHeight = height / 2
        let wavelength = width / CGFloat(frequency)

        var path = Path()
        path.move(to: CGPoint(x: 0, y: midHeight))

        // Draw a sine wave across the width
        for x in stride(from: 0, through: width, by: 1) {
            let relativeX = x / wavelength
            let sine = sin(relativeX + CGFloat(phase))
            // Reduce amplitude to keep wave more contained
            let y = midHeight + sine * midHeight * CGFloat(amplitude) * 0.8
            path.addLine(to: CGPoint(x: x, y: y))
        }

        // Complete the path by adding lines to the bottom corners
        // Only fill to 80% of the height to reduce visual impact
        let bottomY = min(height, midHeight + midHeight * 0.8)
        path.addLine(to: CGPoint(x: width, y: bottomY))
        path.addLine(to: CGPoint(x: 0, y: bottomY))
        path.closeSubpath()

        return path
    }
}

// MARK: - Animated Wave View

/// A view that displays an animated wave that responds to audio levels
struct AnimatedWaveView: View {
    let isRecording: Bool
    let audioLevel: Double
    let waveIndex: Int
    let colors: [Color]
    var animationSpeed: Double = 1.0
    var containedInArea: Bool = true  // New parameter to control if wave is contained
    var maxOpacity: Double = 0.3  // Reduced default opacity

    @State private var phase: Double = 0

    var body: some View {
        WaveShape(
            phase: phase + Double(waveIndex) * 0.5,
            amplitude: isRecording ? audioLevel * 0.3 : 0.05,  // Reduced amplitude
            frequency: 1.0 + Double(waveIndex) * 0.2
        )
        .fill(
            LinearGradient(
                gradient: Gradient(colors: colors),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .opacity(min(maxOpacity, maxOpacity - Double(waveIndex) * 0.1))  // Significantly reduced opacity
        .animation(.easeInOut(duration: 0.3), value: audioLevel)
        .clipShape(Rectangle())  // Ensure wave stays within its container
        .onAppear {
            // Start continuous animation
            withAnimation(
                Animation.linear(duration: 2 / animationSpeed).repeatForever(autoreverses: false)
            ) {
                phase = .pi * 2
            }
        }
    }
}
