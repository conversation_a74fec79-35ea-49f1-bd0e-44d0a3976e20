import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import SwiftUI

#if os(iOS) || os(tvOS)
    import UIKit
#elseif os(macOS)
    import AppKit
#endif

/// A view for speaking and recording affirmations with a modern UI
/// This version uses the FixedSpeakAffirmationViewModel to address repetition counter issues
@available(iOS 17.0, macOS 14.0, *)
public struct FixedSpeakAffirmationView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var viewModel: FixedSpeakAffirmationViewModel
    @StateObject private var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel
    @State private var showingConfirmation = false

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        affirmationService: any AffirmationServiceProtocol
    ) {
        // Create the main view model
        let speakViewModel = FixedSpeakAffirmationViewModel(
            affirmation: affirmation,
            repetitionService: repetitionService,
            audioService: Self.createAudioService(),
            affirmationService: affirmationService
        )

        // Create the tracking view model
        let streakService: StreakServiceProtocol
        do {
            streakService = try ServiceFactory.shared.getStreakService()
        } catch {
            // Fallback to a preview service if the real one fails
            print("Error getting streak service: \(error.localizedDescription)")
            streakService = PreviewServiceFactory.shared.getStreakService()
        }

        // Use the Core version of RepetitionTrackingViewModel
        let repTrackingViewModel = NeuroLoopCore.RepetitionTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioService: Self.createAudioService()
        )

        // Set the tracking view model in the main view model
        speakViewModel.trackingViewModel = repTrackingViewModel

        // Initialize the state objects
        _viewModel = StateObject(wrappedValue: speakViewModel)
        _trackingViewModel = StateObject(wrappedValue: repTrackingViewModel)
    }

    private static func createAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            return AudioRecordingService()
        #endif
    }

    // MARK: - Helper Methods

    // Function to determine what message to display in the status box
    private func getStatusMessage() -> String {
        if viewModel.isRecording {
            if !viewModel.partialRecognitionText.isEmpty {
                return viewModel.partialRecognitionText
            } else {
                return "Listening..."
            }
        } else {
            return "Tap to start recording"
        }
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Background
            BlueBackgroundGradient()
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // Top bar with close button
                HStack {
                    Button(action: {
                        showingConfirmation = true
                    }) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(10)
                            .background(Circle().fill(Color.black.opacity(0.3)))
                    }
                    .padding(.leading)

                    Spacer()

                    // Debug mode toggle (FORCED VISIBLE FOR TESTING)
                    VStack(alignment: .leading, spacing: 2) {
                            Toggle(
                                "Testing Mode: \(viewModel.debugBypassSpeechRecognition ? "ON" : "OFF")",
                                isOn: $viewModel.debugBypassSpeechRecognition
                            )
                            .toggleStyle(SwitchToggleStyle(tint: Color.blue))
                            .padding(.horizontal)

                            if viewModel.debugBypassSpeechRecognition {
                                Text("Testing mode bypasses speech verification")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .padding(.horizontal)

                                // Add direct test buttons
                                HStack {
                                    // Direct Test +1 button using tracking view model
                                    Button(action: {
                                        print(
                                            "🧪 DIRECT TEST: Manually triggering repetition increment using tracking view model"
                                        )
                                        print(
                                            "🧪 Current count before increment: \(trackingViewModel.todayRepetitions)"
                                        )

                                        Task {
                                            // Use the tracking view model's performRepetition method
                                            await trackingViewModel.performRepetition()
                                            print("🧪 Called trackingViewModel.performRepetition()")
                                        }
                                    }) {
                                        Text("Shared Counter +1")
                                            .font(.caption)
                                            .padding(6)
                                            .background(Color.green)
                                            .foregroundColor(.white)
                                            .cornerRadius(4)
                                    }

                                    // Reset counter button
                                    Button(action: {
                                        print("🧪 DIRECT TEST: Resetting repetition count")

                                        Task {
                                            // Get the debug repetition service
                                            let factory = ServiceFactory.shared
                                            if let debugService = try factory.getRepetitionServiceForMode(debugMode: true) as? DebugRepetitionService {
                                                // Reset the count using the debug service
                                                debugService.resetCount(for: viewModel.affirmation.id)
                                                print("🧪 Reset count using debug service")

                                                // Update the tracking view model
                                                await MainActor.run {
                                                    trackingViewModel.updateRepetitionCount(0)
                                                    print("🧪 Reset tracking count to 0")
                                                }
                                            }
                                        }
                                    }) {
                                        Text("Reset Count")
                                            .font(.caption)
                                            .padding(6)
                                            .background(Color.red)
                                            .foregroundColor(.white)
                                            .cornerRadius(4)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        .background(
                            RoundedRectangle(cornerRadius: 8).fill(Color.black.opacity(0.2))
                        )
                        .onChange(of: viewModel.debugBypassSpeechRecognition) {
                            oldValue, newValue in
                            print("FixedSpeakAffirmationView: Testing mode changed to \(newValue)")

                            // Update the tracking view model with the appropriate service
                            Task {
                                let factory = ServiceFactory.shared
                                do {
                                    // Get the appropriate repetition service based on the new debug mode
                                    let service = try factory.getRepetitionServiceForMode(debugMode: newValue)
                                    print(
                                        "FixedSpeakAffirmationView: Got \(newValue ? "debug" : "standard") repetition service: \(type(of: service))"
                                    )

                                    // Update the tracking view model
                                    trackingViewModel.updateRepetitionService(service)
                                    print(
                                        "FixedSpeakAffirmationView: Updated tracking view model with \(newValue ? "debug" : "standard") service"
                                    )
                                } catch {
                                    print(
                                        "FixedSpeakAffirmationView: Error getting repetition service: \(error)"
                                    )
                                }
                            }
                        }
                }
                .padding(.top, 10)

                Spacer(minLength: 40)

                // Affirmation card
                AffirmationTextView(
                    text: viewModel.affirmation.text,
                    showRepeatExactly: true,
                    onRepeatExactly: {
                        // Use playRecording method
                        Task {
                            await viewModel.playRecording()
                        }
                    },
                    isRecording: viewModel.isRecording
                )
                .padding()
                .blueCard()
                .padding(.horizontal)

                // Fixed spacing instead of flexible Spacer
                Spacer().frame(height: 30)

                // Repetition progress - always visible but with opacity change during recording
                RepetitionProgressCardView(
                    viewModel: trackingViewModel,
                    onTap: {
                        // No action - tap disabled to prevent conflicts
                    }
                )
                // Reduce opacity during recording but keep visible
                .opacity(viewModel.isRecording ? 0.6 : 1.0)

                // Fixed spacing instead of flexible Spacer
                Spacer().frame(height: 30)

                // Recording button and status
                VStack(spacing: 15) {
                    // Simplified message text without background box
                    Text(getStatusMessage())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(minWidth: 200, minHeight: 36)

                    // Debug controls removed to prevent conflicts

                    // Recording button
                    ZStack {
                        Button(action: {
                            Task {
                                print(
                                    "🎤 Record button tapped, current state: \(viewModel.isRecording ? "recording" : "not recording")"
                                )

                                if viewModel.isRecording {
                                    // Stop recording if already recording
                                    print("🎤 Stopping recording...")
                                    await viewModel.stopRecording()
                                    print("🎤 Recording stopped - tracking view model will be updated automatically")
                                } else {
                                    // Start recording if not recording
                                    print("🎤 Starting recording...")
                                    viewModel.startRecording()
                                }
                            }
                        }) {
                            ZStack {
                                // Outer ring
                                Circle()
                                    .stroke(Color.blue.opacity(0.7), lineWidth: 4)
                                    .frame(width: 80, height: 80)

                                // Button background
                                Circle()
                                    .fill(viewModel.isRecording ? Color.red : Color.white)
                                    .frame(width: 70, height: 70)
                                    .shadow(radius: 5)

                                // Button icon
                                if viewModel.isRecording {
                                    // Stop icon (square)
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.white)
                                        .frame(width: 20, height: 20)
                                } else {
                                    // Record icon (red circle)
                                    Circle()
                                        .fill(Color.red)
                                        .frame(width: 60, height: 60)
                                }
                            }
                        }

                        // No overlay needed - removed
                    }

                }
                .padding(.bottom, 30)
            }
            .padding()

            // Alert for confirmation
            .alert("Exit Practice?", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) {}
                Button("Exit", role: .destructive) {
                    dismiss()
                }
            } message: {
                Text("Your progress for this session will be saved.")
            }

            // Alert for errors or success messages
            .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
                Alert(
                    title: Text(alertItem.title),
                    message: Text(alertItem.message),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
        // Removed complex notification handling to simplify synchronization
        .onAppear {
            // TEMPORARY: Enable debug mode for testing the counter fix
            viewModel.debugBypassSpeechRecognition = true
            print("📱 VIEW: Debug mode ENABLED for testing")
            print("📱 Testing mode bypasses speech verification")
            print("📱 Use the debug buttons to test counter functionality")

            // Reset any existing repetition count to ensure a clean start
            Task {
                let factory = ServiceFactory.shared
                if let debugService = try factory.getRepetitionServiceForMode(debugMode: true) as? DebugRepetitionService {
                    // Reset the count using the debug service
                    debugService.resetCount(for: viewModel.affirmation.id)
                    print("📱 VIEW: Reset repetition count on app launch")

                    // Update the tracking view model
                    await MainActor.run {
                        trackingViewModel.updateRepetitionCount(0)
                        print("📱 VIEW: Reset tracking view model count to 0")
                    }
                }
            }

            // Initialize the view models
            Task {
                // Initialize the main view model
                await viewModel.initialize()

                // Get the appropriate repetition service based on debug mode
                let factory = ServiceFactory.shared
                do {
                    // Get the repetition service based on current debug mode setting
                    let service = try factory.getRepetitionServiceForMode(
                        debugMode: viewModel.debugBypassSpeechRecognition)
                    print("📱 VIEW: Got repetition service: \(type(of: service))")

                    // Update the tracking view model with the service
                    trackingViewModel.updateRepetitionService(service)
                    print("📱 VIEW: Updated tracking view model with appropriate service")
                } catch {
                    print("📱 VIEW: Error getting repetition service: \(error)")
                }

                // Initialize the tracking view model with the affirmation ID
                await trackingViewModel.loadData(for: viewModel.affirmation.id)

                print("📱 VIEW: Initialized both view models")
                print("📱 TrackingViewModel.todayRepetitions: \(trackingViewModel.todayRepetitions)")
            }
        }
        .onDisappear {
            // Clean up resources when the view disappears
            print("📱 VIEW: onDisappear called - cleaning up resources")

            // Clean up view model resources
            Task {
                await viewModel.cleanup()
                print("📱 Called viewModel.cleanup()")
            }

            // Log final state
            print(
                "📱 Final state - trackingViewModel.todayRepetitions: \(trackingViewModel.todayRepetitions)"
            )
        }
    }
}

// MARK: - Preview

@available(iOS 17.0, macOS 14.0, *)
struct FixedSpeakAffirmationView_Previews: PreviewProvider {
    static var previews: some View {
        FixedSpeakAffirmationView(
            affirmation: AffirmationStub(
                text: "I am confident and capable in everything I do",
                category: .health
            ),
            repetitionService: FullMockRepetitionService(),
            affirmationService: MockAffirmationService()
        )
        .environmentObject(ThemeManager.shared)
    }
}
