import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import Combine

/// A simplified version of the RepetitionProgressCardView that is more reliable
/// and easier to maintain. This view displays the current repetition count and
/// progress towards the daily goal.
public struct RepetitionProgressCardView: View {
    // MARK: - Properties

    // View model for tracking repetition progress
    @ObservedObject var viewModel: NeuroLoopCore.RepetitionTrackingViewModel

    // Animation state
    @State private var animateProgress = false

    // Callback for tap actions
    private let onTap: () -> Void

    /// Primary initializer that takes a view model
    public init(
        viewModel: NeuroLoopCore.RepetitionTrackingViewModel,
        onTap: @escaping () -> Void = {}
    ) {
        self.viewModel = viewModel
        self.onTap = onTap
    }

    /// Legacy initializer for backward compatibility - DEPRECATED
    /// This initializer creates a disconnected view model and should not be used in production
    public init(
        currentCount: Int,
        totalCount: Int,
        currentDay: Int,
        totalDays: Int,
        affirmationId: UUID? = nil,
        onTap: @escaping () -> Void
    ) {
        print("⚠️ WARNING: Using deprecated RepetitionProgressCardView initializer")
        print("⚠️ This creates a disconnected view model that won't update properly")
        print("⚠️ Please use the primary initializer with a proper RepetitionTrackingViewModel")

        // Create a stub affirmation
        let stubAffirmation = AffirmationStub(
            text: "I am confident and capable in everything I do",
            category: .health
        )

        // Create a temporary Core version view model for preview
        let factory = PreviewServiceFactory.shared
        let tempViewModel = NeuroLoopCore.RepetitionTrackingViewModel(
            affirmationService: factory.getAffirmationService(),
            repetitionService: factory.getRepetitionService(),
            streakService: factory.getStreakService(),
            audioService: factory.getAudioRecordingService()
        )

        // Set the values manually
        tempViewModel.updateRepetitionCount(currentCount)

        self.viewModel = tempViewModel
        self.onTap = onTap
    }

    public var body: some View {
        // Use the view model's count directly - no local state needed
        let currentCount = viewModel.todayRepetitions

        ZStack(alignment: .top) {
            // Card background
            RoundedRectangle(cornerRadius: 24, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.blue.opacity(0.85), Color.blue.opacity(0.65),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)

            VStack(spacing: 16) {
                // Title
                Text("Repetition Progress")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .padding(.top, 12)

                // Hidden for cleaner UI
                // Text("Count: \(displayCount)/100")
                //     .font(.system(size: 14))
                //     .fontWeight(.bold)
                //     .foregroundColor(.white)
                //     .padding(.horizontal, 8)
                //     .padding(.vertical, 2)
                //     .background(Color.black.opacity(0.3))
                //     .cornerRadius(4)

                // Progress Counter - use currentCount directly from view model
                RepetitionCounterView(
                    currentCount: currentCount,
                    totalCount: 100,
                    onTap: {
                        // Provide haptic feedback
                        #if os(iOS)
                        let generator = UIImpactFeedbackGenerator(style: .medium)
                        generator.impactOccurred()
                        #endif
                        onTap()
                    }
                )
                .padding(.vertical, 4)
                .frame(height: 90)
                .frame(maxWidth: .infinity)
                .scaleEffect(animateProgress ? 1.05 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: animateProgress)

                // Text showing count out of total - use currentCount directly
                Text("\(currentCount) out of 100")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
                    .padding(.top, 4)
                    .padding(.horizontal, 8)

                Spacer(minLength: 12)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)

            // Streak badge
            HStack(spacing: 4) {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 8))
                Text("Day \(viewModel.currentDay) of 7")
                    .font(.system(size: 8))
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.white.opacity(0.18))
            .clipShape(Capsule())
            .overlay(
                Capsule().stroke(Color.white.opacity(0.25), lineWidth: 0.5)
            )
            .padding([.bottom, .trailing], 8)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottomTrailing)
        }
        .frame(width: 180, height: 200)
        .padding(.vertical, 6)
        .onChange(of: viewModel.todayRepetitions) { oldValue, newValue in
            print("RepetitionProgressCardView: Count changed from \(oldValue) to \(newValue)")

            // Animate when count increases
            if newValue > oldValue {
                withAnimation {
                    animateProgress = true
                }

                // Reset animation after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    animateProgress = false
                }
            }
        }
        .onAppear {
            // Load data when view appears - the Core version will load data automatically
            // if it has an affirmation set, otherwise it will be loaded by the parent view
            Task {
                await viewModel.loadData()
            }
        }
    }
}

#Preview {
    ZStack {
        Color.blue.opacity(0.3).edgesIgnoringSafeArea(.all)
        VStack {
            // Use the legacy initializer for preview
            RepetitionProgressCardView(
                currentCount: 5,
                totalCount: 100,
                currentDay: 1,
                totalDays: 7,
                onTap: {}
            )

            // Show an affirmation card below to demonstrate layout
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .frame(height: 120)
                .padding(.horizontal)
                .overlay(
                    Text("I am confident and capable in everything I do")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                        .padding()
                )
        }
    }
}
