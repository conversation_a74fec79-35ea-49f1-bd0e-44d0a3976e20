import AVFoundation
import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import Speech
import Swift<PERSON>

/**
 * SpeakAffirmationViewModel
 *
 * The main view model for SpeakAffirmationView that handles speech recognition,
 * audio recording, and affirmation verification. This version includes:
 *
 * 1. Proper Debug Mode that bypasses speech recognition
 * 2. Accurate repetition counter increments
 * 3. Audio level detection and display
 * 4. Auto-stop functionality for modern UX
 * 5. Enhanced error handling and user feedback
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class SpeakAffirmationViewModel: ObservableObject {

    // MARK: - Published Properties

    @Published public var isRecording = false
    @Published public var isPlaying = false
    @Published public var spokenText = ""
    @Published public var partialRecognitionText = ""
    @Published public var audioLevel: Float = 0.0
    @Published public var currentAudioLevel: Double = 0.0
    @Published public var alertItem: AlertItem?
    @Published public var debugBypassSpeechRecognition = false
    @Published public var autoStopEnabled = true

    // Progress tracking properties
    @Published public var todayRepetitions: Int = 0
    @Published public var totalRepetitions: Int = 100
    @Published public var currentDay: Int = 1

    // MARK: - Private Properties

    public var affirmation: any AffirmationProtocol
    private var repetitionService: any RepetitionServiceProtocol
    public let audioService: any AudioRecordingServiceProtocol
    private let affirmationService: any AffirmationServiceProtocol
    private let streakService: any StreakServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // Speech recognition
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?

    // Audio monitoring
    private var audioLevelTimer: Timer?
    private var isStoppingRecording = false

    // Reference to the tracking view model for syncing repetition counts
    public var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel?

    // Computed property to get the repetition count from the tracking view model
    public var todayRepetitionsFromTracking: Int {
        return trackingViewModel?.todayRepetitions ?? affirmation.currentRepetitions
    }

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        audioService: any AudioRecordingServiceProtocol,
        affirmationService: any AffirmationServiceProtocol,
        streakService: any StreakServiceProtocol
    ) {
        self.affirmation = affirmation
        self.repetitionService = repetitionService
        self.audioService = audioService
        self.affirmationService = affirmationService
        self.streakService = streakService

        // Print service types for debugging
        print("SpeakAffirmationViewModel: Initialized with services:")
        print("- Repetition Service: \(type(of: repetitionService))")
        print("- Audio Service: \(type(of: audioService))")
        print("- Affirmation Service: \(type(of: affirmationService))")
        print("- Streak Service: \(type(of: streakService))")

        // Initialize progress
        let progressInfo = repetitionService.getProgress(for: affirmation)
        self.todayRepetitions = affirmation.currentRepetitions
        self.totalRepetitions = progressInfo.totalRepetitions
        self.currentDay = progressInfo.currentDay

        print("SpeakAffirmationViewModel: Initial progress - \(todayRepetitions)/\(totalRepetitions), Day \(currentDay)")

        // Set up audio level monitoring
        setupAudioLevelMonitoring()
    }

    // MARK: - Public Methods

    /// Initializes the view model and prepares for recording
    public func initialize() async {
        print("SpeakAffirmationViewModel: initialize called")

        // Load current progress
        await loadProgress()

        // Pre-warm speech recognition
        await prewarmSpeechRecognition()
    }

    /// Cleans up resources when the view model is no longer needed
    public func cleanup() async {
        print("SpeakAffirmationViewModel: cleanup called")

        // Stop any ongoing recording
        if isRecording {
            print("SpeakAffirmationViewModel: Stopping recording during cleanup")
            await stopRecording()
        }

        // Stop audio monitoring
        stopMonitoringAudioLevels()
        print("SpeakAffirmationViewModel: Stopped audio monitoring")

        // Stop speech recognition if active
        await stopSpeechRecognition()
        print("SpeakAffirmationViewModel: Stopped speech recognition")

        // Clean up any other resources
        print("SpeakAffirmationViewModel: Cleanup complete")
    }

    /// Starts recording the user's spoken affirmation
    public func startRecording() {
        print("SpeakAffirmationViewModel: startRecording called")

        Task {
            do {
                // Clear previous spoken text
                spokenText = ""
                partialRecognitionText = ""

                // Start recording
                try await audioService.startRecording()
                isRecording = true

                // Start monitoring audio levels
                startMonitoringAudioLevels()

                // Start speech recognition if not in debug mode
                if !debugBypassSpeechRecognition {
                    await startSpeechRecognition()
                } else {
                    print("SpeakAffirmationViewModel: Debug mode enabled, skipping speech recognition")
                }
            } catch {
                print("SpeakAffirmationViewModel: Error starting recording: \(error.localizedDescription)")
                isRecording = false
                setAlert(AlertItem(
                    title: "Recording Error",
                    message: "Could not start recording: \(error.localizedDescription)"
                ))
            }
        }
    }

    /// Stops recording and processes the spoken affirmation
    public func stopRecording() async {
        print("SpeakAffirmationViewModel: stopRecording called")

        // Prevent multiple rapid calls
        guard !isStoppingRecording else {
            print("SpeakAffirmationViewModel: Already stopping recording, ignoring call")
            return
        }

        isStoppingRecording = true
        defer { isStoppingRecording = false }

        print("SpeakAffirmationViewModel: Current state - isRecording: \(isRecording), debugMode: \(debugBypassSpeechRecognition)")
        print("SpeakAffirmationViewModel: Spoken text: \"\(spokenText)\"")
        print("SpeakAffirmationViewModel: Target text: \"\(affirmation.text)\"")

        // Add a small delay to ensure all speech is captured
        print("SpeakAffirmationViewModel: Adding 0.8 second delay before stopping recording to ensure all speech is captured")
        try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 second delay

        // Capture the current spoken text in case it changes during async operations
        let capturedSpokenText = spokenText
        print("SpeakAffirmationViewModel: Captured spoken text: \"\(capturedSpokenText)\"")

        // Stop speech recognition
        await stopSpeechRecognition()

        // Stop audio recording
        do {
            // Try to stop recording, but handle errors gracefully
            let result = try await audioService.stopRecording()
            print("SpeakAffirmationViewModel: Recording stopped successfully, result: \(result)")

            // Update UI
            isRecording = false

            // In debug mode, always count the repetition
            if debugBypassSpeechRecognition {
                print("SpeakAffirmationViewModel: Debug mode enabled, automatically counting repetition")
                // Add a small delay before recording repetition to ensure UI is ready
                try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                print("SpeakAffirmationViewModel: Delay complete, now recording repetition")
                await recordRepetition()
            } else if !capturedSpokenText.isEmpty {
                // Verify the spoken text matches the affirmation
                print("SpeakAffirmationViewModel: Verifying spoken text against target affirmation")
                let verificationResult = verifyAffirmation(
                    spokenText: capturedSpokenText,
                    targetText: affirmation.text
                )

                if verificationResult.success {
                    print("SpeakAffirmationViewModel: Verification successful, recording repetition")
                    // Add a small delay before recording repetition to ensure UI is ready
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    print("SpeakAffirmationViewModel: Delay complete, now recording repetition")
                    await recordRepetition()
                } else {
                    print("SpeakAffirmationViewModel: Verification failed, not counting repetition")
                    print("SpeakAffirmationViewModel: Similarity: \(verificationResult.similarity * 100)%")

                    // Show a more helpful error message with the similarity percentage
                    let similarityPercent = Int(verificationResult.similarity * 100)
                    setAlert(AlertItem(
                        title: "Not Quite Right",
                        message: "Your spoken text was \(similarityPercent)% similar to the affirmation. Please try again and speak the complete affirmation clearly."
                    ))
                }
            } else {
                print("SpeakAffirmationViewModel: No spoken text detected")
                setAlert(AlertItem(
                    title: "No Speech Detected",
                    message: "Please speak the affirmation clearly when recording."
                ))
            }
        } catch {
            print("SpeakAffirmationViewModel: Error stopping recording: \(error.localizedDescription)")

            // Update UI state
            isRecording = false

            // In debug mode, still count the repetition even if there was an error
            if debugBypassSpeechRecognition {
                print("SpeakAffirmationViewModel: Debug mode enabled, counting repetition despite error")
                Task {
                    // Add a small delay before recording repetition
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    await recordRepetition()
                }
            }
        }
    }

    /// Plays the recorded affirmation
    public func playRecording() async {
        print("SpeakAffirmationViewModel: playRecording called")

        do {
            isPlaying = true
            try await audioService.startPlayback()
            isPlaying = false
        } catch {
            print("SpeakAffirmationViewModel: Error playing recording: \(error.localizedDescription)")
            isPlaying = false
            setAlert(AlertItem(
                title: "Playback Error",
                message: "Could not play recording: \(error.localizedDescription)"
            ))
        }
    }

    /// Resets the repetition count for debugging purposes
    public func resetRepetitionCount() async {
        print("SpeakAffirmationViewModel: resetRepetitionCount called")

        // Reset the tracking view model if available
        if let trackingVM = trackingViewModel {
            await trackingVM.resetTodayRepetitions()

            // Update local state
            todayRepetitions = 0

            // Force UI update
            await MainActor.run {
                objectWillChange.send()
            }
        } else {
            print("SpeakAffirmationViewModel: No tracking view model available to reset")
        }
    }

    /// Forces an increment of the repetition count for debugging purposes
    public func forceIncrementRepetition() async {
        print("SpeakAffirmationViewModel: forceIncrementRepetition called")
        await recordRepetition()
    }

    // MARK: - Private Methods

    /// Records a repetition for the current affirmation
    private func recordRepetition() async {
        print("⭐️ RECORDING REPETITION: Starting process")
        print("⭐️ Current affirmation: \"\(affirmation.text)\"")
        print("⭐️ Affirmation ID: \(affirmation.id)")

        // Prevent multiple simultaneous recording attempts
        guard !isRecording else {
            print("⭐️ Already recording, ignoring duplicate recordRepetition call")
            return
        }

        // Use the tracking view model to perform the repetition
        if let trackingVM = trackingViewModel {
            print("⭐️ Using tracking view model to perform repetition")
            await trackingVM.performRepetition()

            // Update the affirmation from the tracking view model
            if let updatedAffirmation = trackingVM.affirmation {
                affirmation = updatedAffirmation
                print("⭐️ Updated affirmation from tracking view model")
            }

            // Force UI update
            await MainActor.run {
                objectWillChange.send()
                print("⭐️ Sent objectWillChange notification")
            }

            // Success notification is now handled by the view's onChange handler
            await MainActor.run {
                let currentCount = trackingVM.todayRepetitions
                print("🎉 SUCCESS: Repetition recorded successfully")
                print("🎉 New count: \(currentCount) out of \(totalRepetitions)")
                print("🎉 Notification will be shown by view's onChange handler")
            }
        } else {
            print("❌ ERROR: No tracking view model available")
            await MainActor.run {
                setAlert(AlertItem(
                    title: "Error",
                    message: "Unable to record repetition. Please try again."
                ))
            }
        }
    }

    /// Loads the current progress for the affirmation
    private func loadProgress() async {
        print("SpeakAffirmationViewModel: loadProgress called")

        // Get the progress info
        let progressInfo = repetitionService.getProgress(for: affirmation)

        // Update the UI
        totalRepetitions = progressInfo.totalRepetitions
        currentDay = progressInfo.currentDay

        // Update the tracking view model if available
        if let trackingVM = trackingViewModel {
            // Load data in the tracking view model to ensure it has the latest state
            await trackingVM.loadData(for: affirmation.id)
            print("SpeakAffirmationViewModel: Called loadData on trackingViewModel")
        } else {
            print("SpeakAffirmationViewModel: No trackingViewModel available to update")
        }

        // Force UI update
        objectWillChange.send()
    }

    private func prewarmSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Pre-warming speech recognition")

        // Request speech recognition authorization in advance
        if SFSpeechRecognizer.authorizationStatus() == .notDetermined {
            await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { _ in
                    continuation.resume()
                }
            }
        }

        // Initialize the speech recognizer if needed
        if speechRecognizer == nil {
            let preferredLocale = Locale.current
            let localeIdentifier = preferredLocale.language.languageCode?.identifier ?? "en"
            let recognizerLocale = Locale(identifier: "\(localeIdentifier)-US")
            speechRecognizer = SFSpeechRecognizer(locale: recognizerLocale)
        }

        // Pre-configure audio session
        _ = await configureAudioSession(active: false)
    }

    private func setupAudioLevelMonitoring() {
        // Set up audio level monitoring from the audio service
        audioService.recordingPowerPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] level in
                self?.audioLevel = Float(level)
                self?.currentAudioLevel = level
            }
            .store(in: &cancellables)
    }

    private func startMonitoringAudioLevels() {
        // Audio level monitoring is handled by the audio service
        print("SpeakAffirmationViewModel: Audio level monitoring started")
    }

    private func stopMonitoringAudioLevels() {
        // Audio level monitoring is handled by the audio service
        print("SpeakAffirmationViewModel: Audio level monitoring stopped")
    }

    private func setAlert(_ alertItem: AlertItem) {
        self.alertItem = alertItem
    }

    // MARK: - Speech Recognition Methods

    private func startSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Starting speech recognition")

        // Configure audio session for recording
        let audioSessionConfigured = await configureAudioSession(active: true)
        guard audioSessionConfigured else {
            print("SpeakAffirmationViewModel: Failed to configure audio session")
            return
        }

        // Set up speech recognition
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("SpeakAffirmationViewModel: Speech recognizer not available")
            return
        }

        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("SpeakAffirmationViewModel: Failed to create recognition request")
            return
        }

        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.taskHint = .confirmation
        recognitionRequest.contextualStrings = [affirmation.text]

        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }

            Task { @MainActor in
                if let result = result {
                    let recognizedText = result.bestTranscription.formattedString
                    self.partialRecognitionText = recognizedText

                    if result.isFinal {
                        self.spokenText = recognizedText
                        print("SpeakAffirmationViewModel: Final recognition: \"\(recognizedText)\"")

                        // Auto-stop if enabled and text matches
                        if self.autoStopEnabled {
                            let verificationResult = self.verifyAffirmation(
                                spokenText: recognizedText,
                                targetText: self.affirmation.text
                            )

                            if verificationResult.success {
                                print("SpeakAffirmationViewModel: Auto-stop triggered - text matches!")
                                await self.stopRecording()
                            }
                        }
                    }
                }

                if let error = error {
                    print("SpeakAffirmationViewModel: Speech recognition error: \(error.localizedDescription)")
                }
            }
        }

        // Note: Audio buffer data for speech recognition would need to be handled
        // differently since the AudioRecordingServiceProtocol doesn't provide
        // direct access to audio buffers. This would require a different approach
        // or extending the protocol.
    }

    private func stopSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Stopping speech recognition")

        recognitionTask?.cancel()
        recognitionTask = nil

        recognitionRequest?.endAudio()
        recognitionRequest = nil

        // Deactivate audio session
        _ = await configureAudioSession(active: false)
    }

    private func configureAudioSession(active: Bool) async -> Bool {
        do {
            let audioSession = AVAudioSession.sharedInstance()

            if active {
                try audioSession.setCategory(.record, mode: .spokenAudio, options: [])
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            } else {
                try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            }

            return true
        } catch {
            print("SpeakAffirmationViewModel: Audio session configuration error: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - Verification Methods

    private func verifyAffirmation(spokenText: String, targetText: String) -> (success: Bool, similarity: Double) {
        let similarity = calculateSimilarity(spokenText: spokenText, targetText: targetText)
        let threshold = 0.7 // 70% similarity threshold

        print("SpeakAffirmationViewModel: Verification - Similarity: \(similarity * 100)%, Threshold: \(threshold * 100)%")

        return (success: similarity >= threshold, similarity: similarity)
    }

    private func calculateSimilarity(spokenText: String, targetText: String) -> Double {
        let spokenWords = spokenText.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        let targetWords = targetText.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }

        guard !targetWords.isEmpty else { return 0.0 }

        let matchingWords = spokenWords.filter { targetWords.contains($0) }
        return Double(matchingWords.count) / Double(targetWords.count)
    }
}
