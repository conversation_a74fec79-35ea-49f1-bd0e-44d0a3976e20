import AVFoundation
import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
@preconcurrency import Speech
import SwiftData
import SwiftUI

#if os(iOS)
    import UIKit
#endif

// MARK: - ContextualTip Model

/// A model for contextual tips
public struct ContextualTip: Identifiable, Equatable {
    public let id = UUID()
    public let title: String
    public let description: String
    public let iconName: String
    public let actionTitle: String?

    public init(
        title: String,
        description: String,
        iconName: String,
        actionTitle: String? = nil
    ) {
        self.title = title
        self.description = description
        self.iconName = iconName
        self.actionTitle = actionTitle
    }

    public static func == (lhs: ContextualTip, rhs: ContextualTip) -> Bool {
        lhs.id == rhs.id
    }

    // Predefined tips
    public static let speakClearly = ContextualTip(
        title: "Speak Clearly",
        description:
            "For best results, speak clearly and at a normal pace. The app will highlight words as they're recognized.",
        iconName: "mic.fill",
        actionTitle: "Got it"
    )

    public static let autoStop = ContextualTip(
        title: "Auto-Stop Feature",
        description:
            "The recording will automatically stop when you've completed the affirmation or after a brief silence.",
        iconName: "hand.raised.fill",
        actionTitle: "Got it"
    )

    public static let repeatExactly = ContextualTip(
        title: "Repeat Exactly",
        description:
            "Tap 'Repeat Exactly' to hear how the affirmation should be spoken. Try to match the words exactly.",
        iconName: "speaker.wave.2.fill",
        actionTitle: "Got it"
    )

    public static let noisyEnvironment = ContextualTip(
        title: "Noisy Environment Detected",
        description:
            "Background noise may affect speech recognition. Try moving to a quieter location or speaking louder.",
        iconName: "ear.fill",
        actionTitle: "Got it"
    )
}

/// Legacy ViewModel for the SpeakAffirmationView (kept for reference)
@MainActor
public class LegacySpeakAffirmationViewModel: NSObject, ObservableObject {
    // MARK: - Dependencies

    private let affirmationService: any AffirmationServiceProtocol
    public let audioService: any AudioRecordingServiceProtocol
    private let streakService: any StreakServiceProtocol
    private let audioSessionManager = AudioSessionManager.shared

    // MARK: - Speech Recognition Properties

    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    private let speechQueue = DispatchQueue(label: "com.neuroloop.speechQueue")
    private let audioSessionLock = NSLock()
    private var isStoppingRecognition = false

    // MARK: - Haptic Feedback

    #if os(iOS)
        private let selectionHapticGenerator = UISelectionFeedbackGenerator()
        private let successHapticGenerator = UINotificationFeedbackGenerator()
        private let errorHapticGenerator = UINotificationFeedbackGenerator()
    #endif

    // MARK: - Published Properties

    @Published private(set) var affirmation: any AffirmationProtocol
    @Published public var todayRepetitions: Int = 0  // Changed to public for direct binding
    @Published private(set) var totalRepetitions: Int = 100
    @Published private(set) var currentDay: Int = 1
    @Published private(set) var isRecording: Bool = false
    @Published private(set) var isPlaying: Bool = false
    @Published private(set) var spokenText: String = ""
    @Published public var partialRecognitionText: String = ""  // For real-time feedback during speech recognition
    @Published var alertItem: AlertItem?
    @Published public var currentAudioLevel: Double = 0.0  // For displaying audio level in UI

    // Properties for silence detection
    private var lastAudioActivity: Date = Date()
    private var silenceDetectionTask: Task<Void, Never>?
    private var silenceThreshold: Double = 0.05
    private let silenceTimeoutSeconds: Double = 5.0  // Increased from 2.0 to 5.0 seconds to give more time to speak

    // Properties for ambient noise compensation
    private var ambientNoiseLevel: Double = 0.0
    @Published private(set) var isCalibrating: Bool = false
    private let calibrationDuration: TimeInterval = 1.0  // 1 second calibration

    // Flag to disable alerts for testing purposes
    // IMPORTANT: Set to true to disable all alerts, false to enable them
    private let areAlertsDisabled = false  // Enable alerts for production builds

    // Properties for contextual tips
    @Published var currentTip: ContextualTip? = nil
    @Published var showTip: Bool = false
    private var hasShownFirstTimeTip: Bool = false
    private var hasShownAutoStopTip: Bool = false

    // Flag to show recognized text on UI for user feedback
    @Published public var showRecognizedTextOnUI: Bool = true

    // Last notification time to prevent notification overload
    private var lastNotificationTime: Date = Date(timeIntervalSince1970: 0)
    private let notificationThrottleInterval: TimeInterval = 0.5  // 500ms throttle

    // Debug flag to bypass speech recognition for testing
    @Published public var debugBypassSpeechRecognition: Bool = false

    // User preference for auto-stop behavior (default: enabled for modern UX)
    @Published public var autoStopEnabled: Bool = true

    // Error tracking to prevent multiple errors in one session
    private var hasShownErrorForCurrentSession: Bool = false

    // Timestamp for the start of the current recording session
    private var lastRecordingStartTime: Date = Date()

    // Speech recognition timeout
    private var speechRecognitionTimeoutTask: Task<Void, Never>?
    private let speechRecognitionTimeoutInterval: TimeInterval = 30.0  // Increased from 15 to 30 seconds

    // New variable to capture the final spoken text
    private var finalSpokenText: String = ""

    // Reference to the tracking view model for syncing repetition counts
    public var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel?

    // Computed property to get the repetition count from the tracking view model
    public var todayRepetitionsFromTracking: Int {
        return trackingViewModel?.todayRepetitions ?? affirmation.currentRepetitions
    }

    // MARK: - Initialization

    /// Initializes the view model with the given affirmation and services
    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        audioService: any AudioRecordingServiceProtocol,
        affirmationService: any AffirmationServiceProtocol,
        streakService: any StreakServiceProtocol
    ) {
        self.affirmation = affirmation
        self.affirmationService = affirmationService
        self.audioService = audioService
        self.streakService = streakService

        // Initialize with the current repetition count
        self.todayRepetitions = affirmation.currentRepetitions
        self.totalRepetitions = 100  // Default to 100 repetitions

        // Initialize haptic feedback generators
        #if os(iOS)
            selectionHapticGenerator.prepare()
            successHapticGenerator.prepare()
            errorHapticGenerator.prepare()
        #endif

        // Complete initialization
        super.init()

        // Set up speech recognition after super.init
        setupSpeechRecognition()

        // Request speech recognition authorization after super.init
        requestSpeechRecognitionAuthorization()
    }

    deinit {
        // Clean up resources
        // Note: We can't use async/await in deinit, so we'll just cancel any tasks
        speechRecognitionTimeoutTask?.cancel()
    }

    /// Initializes the view model
    public func initialize() async {
        // Request speech recognition authorization
        await requestSpeechRecognitionAuthorization()

        // Clean up any previous resources
        await cleanup()
    }

    /// Cleans up resources when the view model is deallocated
    private func cleanup() async {
        // Stop speech recognition
        if isRecording {
            await stopSpeechRecognition()
        }

        // Cancel any pending tasks
        speechRecognitionTimeoutTask?.cancel()
    }

    // MARK: - Private Methods

    /// Sets up the speech recognizer
    private func setupSpeechRecognition() {
        // Create a speech recognizer with the appropriate locale
        let locale = Locale(identifier: "en-US")
        speechRecognizer = SFSpeechRecognizer(locale: locale)

        // Set the delegate to handle availability changes
        speechRecognizer?.delegate = self
    }

    /// Requests authorization for speech recognition
    private func requestSpeechRecognitionAuthorization() {
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            guard let self = self else { return }

            print(
                "SpeakAffirmationViewModel: Speech recognition authorization status: \(status.rawValue)"
            )

            switch status {
            case .authorized:
                print("SpeakAffirmationViewModel: Speech recognition authorized")
            case .denied:
                print("SpeakAffirmationViewModel: Speech recognition denied")
                DispatchQueue.main.async {
                    self.showAlert(
                        AlertItem(
                            title: "Speech Recognition Access Required",
                            message:
                                "Please allow speech recognition access in Settings to use this feature."
                        ))
                }
            case .restricted:
                print("SpeakAffirmationViewModel: Speech recognition restricted")
            case .notDetermined:
                print("SpeakAffirmationViewModel: Speech recognition not determined")
            @unknown default:
                print("SpeakAffirmationViewModel: Speech recognition unknown status")
            }
        }
    }

    /// Safely configures the audio session for recording - optimized for speech recognition
    private func configureAudioSession(active: Bool) async -> Bool {
        #if os(iOS)
            // Use a lock to prevent multiple simultaneous configurations
            audioSessionLock.lock()
            defer { audioSessionLock.unlock() }

            // Use the AudioSessionManager to configure the session
            if active {
                return await audioSessionManager.activateSession(
                    category: AVAudioSession.Category.playAndRecord,
                    mode: AVAudioSession.Mode.spokenAudio,
                    options: [
                        AVAudioSession.CategoryOptions.allowBluetooth,
                        AVAudioSession.CategoryOptions.defaultToSpeaker,
                    ]
                )
            } else {
                return await audioSessionManager.deactivateSession()
            }
        #else
            // On non-iOS platforms, just return true
            return true
        #endif
    }

    // MARK: - Public Methods

    /// Starts recording the user's spoken affirmation
    public func startRecording() async throws {
        print("SpeakAffirmationViewModel: startRecording called")

        // Add more detailed logging for debugging
        print(
            "SpeakAffirmationViewModel: DEBUG - Speech recognizer available: \(speechRecognizer?.isAvailable ?? false)"
        )
        print(
            "SpeakAffirmationViewModel: DEBUG - Speech recognition authorization status: \(SFSpeechRecognizer.authorizationStatus().rawValue)"
        )

        // Check microphone permission status immediately
        #if os(iOS)
            let audioSession = AVAudioSession.sharedInstance()
            print(
                "SpeakAffirmationViewModel: DEBUG - Microphone recordPermission: \(audioSession.recordPermission.rawValue)"
            )

            // CRITICAL: Test microphone access directly
            print("SpeakAffirmationViewModel: CRITICAL - Testing direct microphone access...")

            // Log current audio session state
            print(
                "SpeakAffirmationViewModel: CRITICAL - Current audio session category: \(audioSession.category.rawValue)"
            )
            print(
                "SpeakAffirmationViewModel: CRITICAL - Current audio session mode: \(audioSession.mode.rawValue)"
            )
            print(
                "SpeakAffirmationViewModel: CRITICAL - Audio input available: \(audioSession.isInputAvailable)"
            )

            // Try to get current input
            if let currentInput = audioSession.currentRoute.inputs.first {
                print(
                    "SpeakAffirmationViewModel: CRITICAL - Current input device: \(currentInput.portName)"
                )
            } else {
                print("SpeakAffirmationViewModel: CRITICAL - No input device available!")

                // Show alert about no input device
                DispatchQueue.main.async {
                    self.showAlert(
                        AlertItem(
                            title: "No Microphone Detected",
                            message:
                                "Your device doesn't have an active microphone input. Please check your device connections and settings."
                        ))
                }
                return
            }

            // Check if we can actually get audio levels
            let testAudioLevel = audioService.recordingPower
            print("SpeakAffirmationViewModel: CRITICAL - Test audio level: \(testAudioLevel) dB")

            // Check permission status
            switch audioSession.recordPermission {
            case .granted:
                print("SpeakAffirmationViewModel: DEBUG - Microphone permission is granted")
            case .denied:
                print("SpeakAffirmationViewModel: DEBUG - Microphone permission is denied")
                DispatchQueue.main.async {
                    self.showAlert(
                        AlertItem(
                            title: "Microphone Access Required",
                            message:
                                "Please allow microphone access in Settings to record your affirmations."
                        ))
                }
                return
            case .undetermined:
                print("SpeakAffirmationViewModel: DEBUG - Microphone permission is undetermined")

                // Request permission explicitly
                audioSession.requestRecordPermission { [weak self] granted in
                    print(
                        "SpeakAffirmationViewModel: CRITICAL - Microphone permission request result: \(granted)"
                    )
                    if !granted {
                        DispatchQueue.main.async {
                            self?.showAlert(
                                AlertItem(
                                    title: "Microphone Access Denied",
                                    message: "You must allow microphone access to use this feature."
                                ))
                        }
                    }
                }
            @unknown default:
                print("SpeakAffirmationViewModel: DEBUG - Microphone permission status is unknown")
            }
        #endif

        // Trigger selection haptic feedback
        #if os(iOS)
            selectionHapticGenerator.selectionChanged()
        #endif

        // Execute UI updates on the main thread
        DispatchQueue.main.async {
            // Clear previous spoken text AND partial recognition text when starting a new recording
            self.spokenText = ""
            self.partialRecognitionText = ""

            // Reset error tracking for the new session
            self.hasShownErrorForCurrentSession = false

            // Set the recording start timestamp to track this session
            self.lastRecordingStartTime = Date()

            // Reset any previous verification state
            print(
                "SpeakAffirmationViewModel: Resetting verification state for new recording at \(self.lastRecordingStartTime)"
            )
        }

        // Make sure speech recognition is set up
        setupSpeechRecognition()

        Task {
            do {
                // Stop any existing speech recognition first
                await stopSpeechRecognition()

                // Start recording immediately - no delay
                print("SpeakAffirmationViewModel: Calling audioService.startRecording()")
                try await audioService.startRecording()
                print("SpeakAffirmationViewModel: Recording started successfully")

                // Set isRecording to true immediately
                await MainActor.run {
                    isRecording = true

                    // Provide haptic feedback for recording start
                    #if os(iOS)
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.success)
                    #endif

                    // Show appropriate contextual tip
                    showAppropriateContextualTip()
                }

                // Start speech recognition immediately in parallel
                Task {
                    print("SpeakAffirmationViewModel: Starting speech recognition...")
                    await startSpeechRecognition()

                    // Add a timeout for speech recognition
                    startSpeechRecognitionTimeout()
                }

                // Start monitoring audio levels in parallel
                Task {
                    startMonitoringAudioLevels()
                }
            } catch {
                print(
                    "SpeakAffirmationViewModel: Error starting recording: \(error.localizedDescription)"
                )
                await MainActor.run {
                    isRecording = false
                    if let audioError = error as? NeuroLoopInterfaces.AudioRecordingError,
                        audioError == .permissionDenied
                    {
                        self.showAlert(
                            AlertItem(
                                title: "Microphone Access Required",
                                message:
                                    "Please allow microphone access in Settings to record your affirmations."
                            ))
                    } else {
                        handleError(error)
                    }
                }
            }
        }
    }

    /// Stops recording and processes the spoken affirmation
    public func stopRecording() async {
        print("SpeakAffirmationViewModel: stopRecording called, isRecording: \(isRecording)")

        // Prevent multiple calls to stopRecording
        guard isRecording else {
            print("SpeakAffirmationViewModel: Already stopped recording, ignoring call")
            return
        }

        // Update UI on main thread first
        await MainActor.run {
            isRecording = false
        }

        // Stop audio recording first
        do {
            _ = try await audioService.stopRecording()
            print("SpeakAffirmationViewModel: Audio recording stopped successfully")
        } catch {
            print("SpeakAffirmationViewModel: Error stopping audio recording: \(error)")
        }

        // Stop speech recognition
        await stopSpeechRecognition()

        // Cancel any pending timeout
        speechRecognitionTimeoutTask?.cancel()

        // Provide haptic feedback for recording stop
        #if os(iOS)
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
        #endif

        // Process the spoken text if we have any
        // Use the captured finalSpokenText instead of capturedSpokenText
        if !finalSpokenText.isEmpty {
            print("SpeakAffirmationViewModel: Processing spoken text: \(finalSpokenText)")

            // Check if the spoken text matches the affirmation
            let matchResult = checkAffirmationMatch(spokenText: finalSpokenText)

            if matchResult.isMatch || debugBypassSpeechRecognition {
                print("SpeakAffirmationViewModel: Match found! Processing...")

                // Process the successful match
                await processSuccessfulMatch()
            } else {
                print(
                    "SpeakAffirmationViewModel: No match found. Similarity: \(matchResult.similarity)%"
                )

                // Show alert about no match
                await MainActor.run {
                    showAlert(
                        AlertItem(
                            title: "Not Quite Right",
                            message:
                                "Please try again and make sure you're saying the exact affirmation."
                        ))

                    // Update UI with recognized text if enabled
                    if showRecognizedTextOnUI {
                        partialRecognitionText = "❌ No match. Try again."
                    }
                }

                // Trigger error haptic feedback
                #if os(iOS)
                    errorHapticGenerator.notificationOccurred(.error)
                #endif
            }
        } else if debugBypassSpeechRecognition {
            print("SpeakAffirmationViewModel: Debug mode enabled, processing without speech recognition")
            await processSuccessfulMatch()
        } else {
            print("SpeakAffirmationViewModel: No spoken text to process")

            // Show alert about no speech detected
            await MainActor.run {
                showAlert(
                    AlertItem(
                        title: "No Speech Detected",
                        message:
                            "We couldn't hear you speaking. Please try again and speak clearly into the microphone."
                    ))

                // Update UI with recognized text if enabled
                if showRecognizedTextOnUI {
                    partialRecognitionText = "❌ No speech detected. Try again."
                }
            }

            // Trigger error haptic feedback
            #if os(iOS)
                errorHapticGenerator.notificationOccurred(.error)
            #endif
        }

        // Reset session error flag for next recording
        hasShownErrorForCurrentSession = false

        // Clear the final spoken text for next recording
        finalSpokenText = ""

        print("SpeakAffirmationViewModel: stopRecording completed")
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension SpeakAffirmationViewModel: SFSpeechRecognizerDelegate {
    nonisolated public func speechRecognizer(
        _ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool
    ) {
        print("SpeakAffirmationViewModel: Speech recognizer availability changed to \(available)")
    }
}

// MARK: - Helper Methods

extension SpeakAffirmationViewModel {
    /// Shows an alert with the given item
    func showAlert(_ item: AlertItem) {
        // Skip alerts if they're disabled for testing
        if areAlertsDisabled {
            print(
                "SpeakAffirmationViewModel: Alert suppressed (disabled for testing): \(item.title)")
            return
        }

        // Execute on the main thread
        DispatchQueue.main.async {
            self.alertItem = item
        }
    }

    /// Handles an error by showing an alert
    func handleError(_ error: Error) {
        print("SpeakAffirmationViewModel: Error: \(error.localizedDescription)")

        // Show an alert with the error
        showAlert(
            AlertItem(
                title: "Error",
                message: error.localizedDescription
            ))
    }

    /// Starts a timeout for speech recognition
    func startSpeechRecognitionTimeout() {
        // Cancel any existing timeout
        speechRecognitionTimeoutTask?.cancel()

        // Start a new timeout
        speechRecognitionTimeoutTask = Task { [weak self] in
            guard let self = self else { return }

            // Wait for the timeout interval
            try? await Task.sleep(
                nanoseconds: UInt64(speechRecognitionTimeoutInterval * 1_000_000_000))

            // Check if we're still recording
            if self.isRecording && !Task.isCancelled {
                print("SpeakAffirmationViewModel: Speech recognition timeout")

                // Stop recording
                await MainActor.run {
                    // Only show the timeout alert if we haven't shown an error yet and we're still recording
                    if !self.hasShownErrorForCurrentSession && self.isRecording {
                        self.showAlert(
                            AlertItem(
                                title: "Recording Timeout",
                                message: "Recording stopped due to inactivity. Please try again."
                            ))
                    }

                    // Stop recording only if we're still recording
                    if self.isRecording {
                        Task {
                            await self.stopRecording()
                        }
                    }
                }
            }
        }
    }

    /// Starts speech recognition
    private func startSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Starting speech recognition")

        // Check if speech recognition is available
        guard let recognizer = speechRecognizer, recognizer.isAvailable else {
            print("SpeakAffirmationViewModel: Speech recognizer not available")
            return
        }

        // Check if we have authorization
        guard SFSpeechRecognizer.authorizationStatus() == .authorized else {
            print("SpeakAffirmationViewModel: Speech recognition not authorized")
            return
        }

        // Use a barrier to ensure exclusive access to speech recognition
        await withCheckedContinuation { continuation in
            speechQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }

                Task {
                    do {
                        // Create a new recognition request
                        let request = SFSpeechAudioBufferRecognitionRequest()
                        self.recognitionRequest = request

                        // Configure the request for maximum responsiveness
                        request.shouldReportPartialResults = true
                        request.taskHint = .dictation

                        if #available(iOS 16.0, macOS 13.0, *) {
                            // Use the affirmation text as a contextual string
                            request.contextualStrings = [await self.affirmation.text]
                            print(
                                "SpeakAffirmationViewModel: Added contextual string: \(await self.affirmation.text)"
                            )
                        }

                        // Create an input node for the audio engine
                        let inputNode = self.audioEngine.inputNode

                        // Install a tap on the input node with smaller buffer size for faster response
                        let recordingFormat = inputNode.outputFormat(forBus: 0)
                        inputNode.installTap(onBus: 0, bufferSize: 512, format: recordingFormat) {
                            buffer, time in
                            Task { @MainActor in
                                self.recognitionRequest?.append(buffer)
                            }
                        }

                        // Start the audio engine
                        self.audioEngine.prepare()
                        try self.audioEngine.start()
                        print("SpeakAffirmationViewModel: Audio engine started")

                        // Start the recognition task
                        self.recognitionTask = recognizer.recognitionTask(with: request) {
                            [weak self] result, error in
                            Task { @MainActor in
                                guard let self = self else { return }

                                if let error = error {
                                    print("SpeakAffirmationViewModel: Speech recognition error: \(error.localizedDescription)")
                                    // Only show critical errors that affect functionality
                                    let speechError = error as NSError
                                    let errorCode = speechError.code
                                    // SFSpeechRecognizerError codes: 203 = taskNotValid, 204 = invalidRecognitionMetadata
                                    if errorCode == 203 || errorCode == 204 {
                                        if self.isRecording && !self.hasShownErrorForCurrentSession {
                                            self.alertItem = AlertItem(
                                                title: "Speech Recognition Error",
                                                message: "Please try again. If the problem persists, restart the app."
                                            )
                                            self.hasShownErrorForCurrentSession = true
                                        }
                                    }
                                    return
                                }

                                // Handle recognition result
                                if let result = result {
                                    // Get the best transcription
                                    let transcription = result.bestTranscription.formattedString

                                    // Update the UI on the main thread
                                    Task { @MainActor in
                                        // Get the previous spoken text to check for new words
                                        let previousText = self.spokenText

                                        // Update the spoken text
                                        self.spokenText = transcription

                                        // Update the partial recognition text if enabled
                                        if self.showRecognizedTextOnUI {
                                            self.partialRecognitionText = transcription
                                        }

                                        // Check if new words were recognized
                                        if transcription.count > previousText.count {
                                            // Provide subtle haptic feedback for new words
                                            #if os(iOS)
                                                let generator = UIImpactFeedbackGenerator(style: .light)
                                                generator.impactOccurred(intensity: 0.5)
                                            #endif
                                        }

                                        // Check for a match if this is the final result
                                        if result.isFinal {
                                            print(
                                                "SpeakAffirmationViewModel: Final recognition result: \(transcription)"
                                            )

                                            // Capture the final spoken text
                                            self.finalSpokenText = transcription

                                            // Check if the spoken text matches the affirmation
                                            let (isMatch, similarity) = self.checkAffirmationMatch(
                                                spokenText: transcription)
                                            print(
                                                "SpeakAffirmationViewModel: Match check - isMatch: \(isMatch), similarity: \(similarity)%"
                                            )

                                            // Update UI to show match status
                                            if self.showRecognizedTextOnUI {
                                                if isMatch {
                                                    self.partialRecognitionText = "✅ Perfect match! Processing..."
                                                } else if similarity >= 60 {
                                                    self.partialRecognitionText = "✅ Good match (\(Int(similarity))%)! Processing..."
                                                } else {
                                                    self.partialRecognitionText = "❌ No match (\(Int(similarity))%). Keep speaking or tap stop."
                                                }
                                            }

                                            // If we have a good match (70%+) and auto-stop is enabled, auto-stop recording
                                            // Only auto-stop if we're still recording to prevent multiple calls
                                            if isMatch && self.isRecording && self.autoStopEnabled {
                                                print(
                                                    "SpeakAffirmationViewModel: Auto-stopping recording due to excellent match (\(similarity)%)"
                                                )

                                                // Add a small delay to let user see the "Perfect match!" message
                                                Task {
                                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                                                    await self.stopRecording()
                                                }
                                            } else if isMatch && self.isRecording && !self.autoStopEnabled {
                                                print(
                                                    "SpeakAffirmationViewModel: Match found (\(similarity)%) but auto-stop disabled. User must tap stop."
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        print("SpeakAffirmationViewModel: Speech recognition started")
                    } catch {
                        print(
                            "SpeakAffirmationViewModel: Error starting speech recognition: \(error.localizedDescription)"
                        )

                        // Show error alert
                        await MainActor.run {
                            self.showAlert(
                                AlertItem(
                                    title: "Speech Recognition Error",
                                    message:
                                        "Could not start speech recognition: \(error.localizedDescription)"
                                ))
                        }
                    }

                    // Resume the continuation
                    continuation.resume()
                }
            }
        }
    }

    /// Stops speech recognition
    public func stopSpeechRecognition() async {
        // Use a barrier to ensure exclusive access to speech recognition
        await withCheckedContinuation { continuation in
            speechQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }

                Task {
                    await self.stopSpeechRecognitionInternal()
                    continuation.resume()
                }
            }
        }
    }

    /// Internal method to stop speech recognition
    private func stopSpeechRecognitionInternal() async {
        // Check if we're already stopping
        guard !isStoppingRecognition else {
            return
        }

        // Set the flag to prevent multiple stops
        isStoppingRecognition = true

        // Cancel the recognition task
        if let task = recognitionRequest {
            task.endAudio()
            recognitionRequest = nil
        }

        // Cancel the recognition task
        if let task = recognitionTask {
            task.cancel()
            recognitionTask = nil
        }

        // Stop the audio engine
        if audioEngine.isRunning {
            // Remove the tap from the input node
            audioEngine.inputNode.removeTap(onBus: 0)

            // Stop the audio engine
            audioEngine.stop()
        }

        // Reset the flag
        isStoppingRecognition = false
    }

    /// Monitors audio levels to check if microphone is picking up sound
    func startMonitoringAudioLevels() {
        // Cancel any existing silence detection task
        silenceDetectionTask?.cancel()

        // Reset the last audio activity timestamp
        lastAudioActivity = Date()

        // Start a new task for monitoring audio levels
        Task { [weak self] in
            guard let self = self else { return }

            while self.isRecording && !Task.isCancelled {
                // Get the current audio level
                let power = self.audioService.recordingPower

                // Update the UI with the current audio level
                await MainActor.run {
                    self.currentAudioLevel = power

                    // Check if there's audio activity
                    if power > self.silenceThreshold {
                        // Update the last audio activity timestamp
                        self.lastAudioActivity = Date()
                    } else {
                        // Check if we've been silent for too long
                        let silenceDuration = Date().timeIntervalSince(self.lastAudioActivity)

                        // If we've been silent for longer than the timeout and we have some spoken text,
                        // auto-stop the recording
                        if silenceDuration > self.silenceTimeoutSeconds && !self.spokenText.isEmpty && self.isRecording
                        {
                            print(
                                "SpeakAffirmationViewModel: Auto-stopping recording due to silence (\(silenceDuration) seconds)"
                            )

                            // Only auto-stop if we have at least some recognized text and we're still recording
                            Task {
                                await self.stopRecording()
                            }

                            // Return from the task to stop the loop
                            return
                        }
                    }
                }

                // Wait a moment before checking again
                try? await Task.sleep(nanoseconds: 100_000_000)  // 0.1 seconds
            }
        }
    }

    /// Checks if the spoken text matches the affirmation
    func checkAffirmationMatch(spokenText: String) -> (isMatch: Bool, similarity: Double) {
        // Get the affirmation text
        let affirmationText = affirmation.text.lowercased()
        let normalizedSpokenText = spokenText.lowercased()

        // Safety check for empty strings
        if affirmationText.isEmpty || normalizedSpokenText.isEmpty {
            print("SpeakAffirmationViewModel: Empty string detected in match check")
            return (false, 0.0)
        }

        // Calculate similarity using a safer method
        let similarity = calculateWordOverlapSimilarity(affirmationText, normalizedSpokenText)

        // Check if it's a match
        let isMatch = similarity >= 0.7  // 70% similarity threshold

        print(
            "SpeakAffirmationViewModel: Match check - similarity: \(similarity * 100)%, isMatch: \(isMatch)"
        )
        return (isMatch, similarity * 100)
    }

    /// Calculates the similarity between two strings using word overlap
    /// This is a safer implementation than Levenshtein distance
    private func calculateWordOverlapSimilarity(_ s1: String, _ s2: String) -> Double {
        // Safety check
        if s1.isEmpty || s2.isEmpty {
            return 0.0
        }

        // If strings are identical, return 1.0
        if s1 == s2 {
            return 1.0
        }

        // Split into words and create sets
        let words1 = Set(s1.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty })
        let words2 = Set(s2.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty })

        // Safety check for empty sets
        if words1.isEmpty || words2.isEmpty {
            return 0.0
        }

        // Calculate Jaccard similarity (intersection over union)
        let intersection = words1.intersection(words2).count
        let union = words1.union(words2).count

        return Double(intersection) / Double(union)
    }

    /// Legacy Levenshtein distance calculation - not used anymore due to potential crashes
    private func levenshteinDistance(_ s1: String, _ s2: String) -> Int {
        // Safety check for empty strings
        if s1.isEmpty {
            return s2.count
        }
        if s2.isEmpty {
            return s1.count
        }

        let s1Array = Array(s1)
        let s2Array = Array(s2)
        let s1Length = s1Array.count
        let s2Length = s2Array.count

        // Create matrix with safe dimensions
        var matrix = [[Int]](
            repeating: [Int](repeating: 0, count: s2Length + 1), count: s1Length + 1)

        // Initialize first row and column
        for i in 0...s1Length {
            matrix[i][0] = i
        }

        for j in 0...s2Length {
            matrix[0][j] = j
        }

        // Fill the matrix
        for i in 1...s1Length {
            for j in 1...s2Length {
                let cost = s1Array[i - 1] == s2Array[j - 1] ? 0 : 1
                matrix[i][j] = min(
                    matrix[i - 1][j] + 1,  // Deletion
                    matrix[i][j - 1] + 1,  // Insertion
                    matrix[i - 1][j - 1] + cost  // Substitution
                )
            }
        }

        return matrix[s1Length][s2Length]
    }

    /// Processes a successful match
    func processSuccessfulMatch() async {
        print("SpeakAffirmationViewModel: Processing successful match")

        do {
            // 1. Update the UI immediately for responsive feedback
            // Provide haptic feedback for success
            #if os(iOS)
                successHapticGenerator.notificationOccurred(.success)
            #endif

            // Update debug text if enabled
            if showRecognizedTextOnUI {
                partialRecognitionText = "✅ Match found! Incrementing count..."
            }

            // 2. Record the repetition using the tracking view model (SINGLE SOURCE OF TRUTH)
            let updatedAffirmation: any AffirmationProtocol
            if let trackingVM = trackingViewModel {
                // Use the tracking view model to perform the repetition
                print("🎤 SPEECH: Using tracking view model to perform repetition")
                await trackingVM.performRepetition()

                // Get the updated affirmation from the tracking view model
                guard let updatedAff = trackingVM.affirmation else {
                    throw AffirmationError.unknown
                }
                updatedAffirmation = updatedAff

                // Update our local affirmation reference
                self.affirmation = updatedAffirmation
                print("🎤 SPEECH: Updated local affirmation, new count: \(updatedAffirmation.currentRepetitions)")
            } else {
                // Fallback to direct service call if tracking view model is not available
                print("🎤 SPEECH: No tracking view model, using direct service call")
                let updatedAff = try await affirmationService.recordRepetition(for: affirmation)
                updatedAffirmation = updatedAff
                self.affirmation = updatedAffirmation
            }

            // 3. Update the streak
            _ = await streakService.validateStreak(for: updatedAffirmation)

            // 4. Update the UI
            // Update the affirmation reference
            self.affirmation = updatedAffirmation

            // Add a significant delay to ensure recording UI has fully updated
            // This is critical for ensuring the UI updates in the correct order
            try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 seconds

            print(
                "SpeakAffirmationViewModel: Delay completed, updating UI with new count: \(updatedAffirmation.currentRepetitions)"
            )

            // Update the UI directly on the main thread
            await MainActor.run {
                self.todayRepetitions = updatedAffirmation.currentRepetitions
                self.objectWillChange.send()  // Force UI update

                // Post a notification to ensure all observers are updated
                NotificationCenter.default.post(
                    name: Notification.Name("RepetitionCountChanged"),
                    object: nil,
                    userInfo: [
                        "affirmationId": updatedAffirmation.id,
                        "count": updatedAffirmation.currentRepetitions,
                    ]
                )
            }

            // Update debug text if enabled
            if self.showRecognizedTextOnUI {
                self.partialRecognitionText =
                    "✅ Success! Count: \(todayRepetitions)/\(self.totalRepetitions)"
            }

            // Get the updated count directly from the affirmation object
            let updatedCount = updatedAffirmation.currentRepetitions

            // Make sure todayRepetitions is in sync with the affirmation
            if self.todayRepetitions != updatedCount {
                print(
                    "SpeakAffirmationViewModel: Fixing count discrepancy - todayRepetitions: \(self.todayRepetitions), affirmation.currentRepetitions: \(updatedCount)"
                )
                self.todayRepetitions = updatedCount
            }

            // Success notification is now handled by the view's onChange handler
            print("🎉 SUCCESS: Repetition recorded successfully")
            print("🎉 New count: \(updatedCount) out of \(self.totalRepetitions)")
            print("🎉 Notification will be shown by view's onChange handler")
        } catch {
            print(
                "SpeakAffirmationViewModel: Error processing match: \(error.localizedDescription)")

            // Update debug text if enabled
            if showRecognizedTextOnUI {
                partialRecognitionText = "❌ Error: \(error.localizedDescription)"
            }

            // Show error alert
            showAlert(
                AlertItem(
                    title: "Error",
                    message: "Could not update repetition count: \(error.localizedDescription)"
                ))

            // Trigger error haptic feedback
            #if os(iOS)
                errorHapticGenerator.notificationOccurred(.error)
            #endif
        }
    }

    /// Plays the recorded affirmation
    public func playRecording() async {
        print("SpeakAffirmationViewModel: Playing recording")

        do {
            isPlaying = true
            try await audioService.startPlayback()
            isPlaying = false
        } catch {
            print(
                "SpeakAffirmationViewModel: Error playing recording: \(error.localizedDescription)")
            isPlaying = false
            showAlert(
                AlertItem(
                    title: "Playback Error",
                    message: "Could not play recording: \(error.localizedDescription)"
                ))
        }
    }

    /// Resets the repetition count for the current affirmation
    public func resetRepetitionCount() async {
        print("SpeakAffirmationViewModel: Resetting repetition count")

        do {
            // Create a custom method to reset the repetition count
            // Since we don't have direct access to reset the count, we'll use a workaround
            // First, start a new cycle for the affirmation
            try await affirmationService.startCycle(for: affirmation)

            // Then fetch the updated affirmation
            if let updatedAffirmation = try await affirmationService.fetchAffirmation(
                id: affirmation.id)
            {
                // Update the affirmation reference
                self.affirmation = updatedAffirmation

                // Update the UI directly
                self.todayRepetitions = updatedAffirmation.currentRepetitions
                self.objectWillChange.send()  // Force UI update

                // Show success message
                self.showAlert(
                    AlertItem(
                        title: "Reset Complete",
                        message: "Repetition count has been reset to 0."
                    ))
            }
        } catch {
            print(
                "SpeakAffirmationViewModel: Error resetting repetition count: \(error.localizedDescription)"
            )

            // Show error alert
            self.showAlert(
                AlertItem(
                    title: "Error",
                    message: "Could not reset repetition count: \(error.localizedDescription)"
                ))
        }
    }

    /// Pre-warms the speech recognition system to reduce delay when user starts recording
    public func prewarmSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Pre-warming speech recognition")

        // Request speech recognition authorization in advance
        if SFSpeechRecognizer.authorizationStatus() == .notDetermined {
            await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { _ in
                    continuation.resume()
                }
            }
        }

        // Initialize the speech recognizer if needed
        if speechRecognizer == nil {
            setupSpeechRecognition()
        }

        // Pre-configure audio session
        _ = await configureAudioSession(active: false)

        // Calibrate for ambient noise
        await calibrateAmbientNoise()

        print("SpeakAffirmationViewModel: Speech recognition pre-warmed")
    }

    /// Calibrates the microphone for ambient noise
    private func calibrateAmbientNoise() async {
        print("SpeakAffirmationViewModel: Calibrating for ambient noise...")

        // Set calibration flag
        isCalibrating = true

        // Configure audio session for calibration
        guard await configureAudioSession(active: true) else {
            print("SpeakAffirmationViewModel: Failed to configure audio session for calibration")
            isCalibrating = false
            return
        }

        // Start a short recording to measure ambient noise
        do {
            // Start recording
            try await audioService.startRecording()

            // Collect samples for the calibration duration
            var samples: [Double] = []
            let startTime = Date()

            while Date().timeIntervalSince(startTime) < calibrationDuration {
                // Get the current audio level
                let power = audioService.recordingPower
                samples.append(power)

                // Wait a moment before checking again
                try? await Task.sleep(nanoseconds: 100_000_000)  // 0.1 seconds
            }

            // Stop recording
            _ = try await audioService.stopRecording()

            // Calculate the average ambient noise level
            if !samples.isEmpty {
                ambientNoiseLevel = samples.reduce(0, +) / Double(samples.count)

                // Adjust the silence threshold based on ambient noise
                // Add a buffer to the ambient noise level
                silenceThreshold = max(0.05, ambientNoiseLevel + 0.1)

                print(
                    "SpeakAffirmationViewModel: Ambient noise level: \(ambientNoiseLevel), adjusted silence threshold: \(silenceThreshold)"
                )
            }
        } catch {
            print(
                "SpeakAffirmationViewModel: Error during calibration: \(error.localizedDescription)"
            )
        }

        // Reset calibration flag
        isCalibrating = false
    }

    /// Forces an increment of the repetition count for the current affirmation
    public func forceIncrementRepetition() async {
        print("SpeakAffirmationViewModel: Forcing increment of repetition count")

        // If we're recording, stop recording first
        if isRecording {
            print("SpeakAffirmationViewModel: Currently recording, stopping recording first")
            await stopRecording()

            // Add a significant delay to ensure recording UI has fully updated
            print("SpeakAffirmationViewModel: Adding delay after stopping recording")
            try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 seconds
        } else {
            print("SpeakAffirmationViewModel: Not recording, proceeding directly to increment")
        }

        // Add another small delay before processing the match
        // This ensures any UI updates from stopping recording have completed
        try? await Task.sleep(nanoseconds: 200_000_000)  // 0.2 seconds
        print("SpeakAffirmationViewModel: Now processing successful match")

        await processSuccessfulMatch()
    }

    /// Updates the audio level
    @MainActor private func updateAudioLevel() {
        // Get the current audio level from the audio service
        currentAudioLevel = audioService.recordingPower
    }

    /// Shows a contextual tip
    @MainActor func showContextualTip(_ tip: ContextualTip) {
        currentTip = tip
        showTip = true
    }

    /// Shows the appropriate contextual tip based on the current state
    @MainActor func showAppropriateContextualTip() {
        // Don't show tips if alerts are disabled (for testing)
        if areAlertsDisabled {
            return
        }

        // First-time user tip
        if !hasShownFirstTimeTip {
            showContextualTip(ContextualTip.speakClearly)
            hasShownFirstTimeTip = true
            return
        }

        // Auto-stop tip (show after first successful recording)
        if !hasShownAutoStopTip && todayRepetitions > 0 {
            showContextualTip(ContextualTip.autoStop)
            hasShownAutoStopTip = true
            return
        }

        // Noisy environment tip (show if ambient noise is high)
        if ambientNoiseLevel > 0.2 {
            showContextualTip(ContextualTip.noisyEnvironment)
            return
        }
    }
}
