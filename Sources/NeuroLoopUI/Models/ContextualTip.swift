import Foundation

/// A model for contextual tips displayed to users
public struct ContextualTip {
    public let id: UUID
    public let title: String
    public let description: String
    public let iconName: String
    public let actionTitle: String?
    
    public init(
        id: UUID = UUID(),
        title: String,
        description: String,
        iconName: String,
        actionTitle: String? = nil
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.iconName = iconName
        self.actionTitle = actionTitle
    }
}

// MARK: - Predefined Tips

public extension ContextualTip {
    static let speakClearly = ContextualTip(
        title: "Speak Clearly",
        description: "Pronounce each word clearly and with conviction for better recognition.",
        iconName: "text.quote",
        actionTitle: "Got it"
    )
    
    static let autoStop = ContextualTip(
        title: "Auto-Stop Feature",
        description: "Recording will automatically stop when your affirmation is recognized.",
        iconName: "stop.circle",
        actionTitle: "Understood"
    )
    
    static let noisyEnvironment = ContextualTip(
        title: "Quiet Environment",
        description: "For best results, practice in a quiet environment with minimal background noise.",
        iconName: "speaker.slash",
        actionTitle: "OK"
    )
}
